
#ifndef __MINIEYE_EXPAND_MESSAGE_H__
#define __MINIEYE_EXPAND_MESSAGE_H__


#include <vector>
#include <string>
#include <msgpack.h>
#include <msgpack.hpp>

#define EXPAND_LIBFLOW_PORT          "23877"
#define TAHSENSOR_LIBFLOW_TOPIC      "tahsensor"

#define WEIGHTSENSOR_LIBFLOW_TOPIIC     "weightsensor_SAHX_120B"
#define WEIGHTSENSORZHF03_LIBFLOW_TOPIIC     "weightsensor_ZHF03"


#define TPMS_LIBFLOW_TOPIC      "TPMS.data.raw"
#define FUEL_LIBFLOW_RAW_TOPIC                  "FUEL.data.raw"

#define XIAMEN_SHUOQI_LIBFLOW_TOPIC             "XiaMenShuoQi"

#define ALGO_CAN_DISPLAY_OUTPUT_PROC_PROPERTY   "rw.algo.can.display.output.proc"

namespace expand
{

class ExpandMessageBase
{
public:
    ExpandMessageBase(std::string type) : mType(type) {
        mUtcTime = 0;
    }
    virtual ~ExpandMessageBase() {};
    std::string mType;
    uint64_t mUtcTime = 0;
    MSGPACK_DEFINE(mType, mUtcTime);
};


/*
    mesgpack test code

    // pack
    expand::TAHSensorMessage inMessage;
    inMessage.mTemps.push_back(22.0);
    inMessage.mTemps.push_back(33.0);
    inMessage.mTemps.push_back(44.0);
    inMessage.mTemps.push_back(55.0);
    inMessage.mHumis.push_back(66.0);
    inMessage.mHumis.push_back(77.0);
    inMessage.mHumis.push_back(88.0);
    inMessage.mHumis.push_back(99.0);
    const char* tmp = "msgpack";
    inMessage.mRawData.ptr  = tmp;
    inMessage.mRawData.size = strlen(tmp) + 1;
    msgpack::sbuffer  sbuf;
    msgpack::pack(sbuf, inMessage);


    // unpack
    msgpack::object_handle unpack = msgpack::unpack(sbuf.data(), sbuf.size());
    msgpack::object  obj = unpack.get();
    expand::TAHSensorMessage outMessage  = obj.as<expand::TAHSensorMessage>();
    logi("%s, %f %f, %d, %s", outMessage.mType.c_str(), outMessage.mTemps[1], outMessage.mHumis[2],
            outMessage.mRawData.size, outMessage.mRawData.ptr);


*/

// temperature and humidity
class TAHSensorMessage : public ExpandMessageBase
{
#define TAH_SENSOR_NOEXIST_VALUE (255.0)
public:
    TAHSensorMessage() : ExpandMessageBase("TAHSensor") {};
    ~TAHSensorMessage() {};

    std::vector<double> mTemps;
    std::vector<double> mHumis;
    msgpack::type::raw_ref  mRawData;
    MSGPACK_DEFINE(MSGPACK_BASE(ExpandMessageBase), mTemps, mHumis, mRawData);
};

// temperature and humidity
class weightSensorMessage : public ExpandMessageBase
{
#define TAH_SENSOR_NOEXIST_VALUE (255.0)
public:
    weightSensorMessage() : ExpandMessageBase("WeightSensor") {};
    ~weightSensorMessage() {};
    bool    mStatus;//称重传感器是否在线
    bool    mIsCalib;//称重传感器是否标定
    uint16_t mUnit;//单位0-0.1Kg；1-1kg；2-10kg；3-100kg
    uint32_t mWeight;
    uint32_t mWeightAD;
    MSGPACK_DEFINE(MSGPACK_BASE(ExpandMessageBase), mStatus,        mIsCalib, mUnit, mWeight, mWeightAD);
};

class weightSensorZHF03Message : public ExpandMessageBase
{
#define TAH_SENSOR_NOEXIST_VALUE (255.0)
public:
    weightSensorZHF03Message() : ExpandMessageBase("WeightSensorZHF03") {};
    ~weightSensorZHF03Message() {};
    bool    mStatus;// 载重稳定标志位
    bool    mIsCalib;//称重传感器是否标定
    uint16_t mUnit;//单位0-0.1Kg；1-1kg；2-10kg；3-100kg
    uint32_t mWeight;
    uint16_t mTorque;   /* 最大扭矩 nm */
    uint16_t mVersion; /* 版本号 */
    std::vector<uint8_t> mRaw; /* 原始帧 */
    MSGPACK_DEFINE(MSGPACK_BASE(ExpandMessageBase), mStatus,         mIsCalib, mUnit, mWeight, mTorque, mVersion, mRaw);
};

class FuelMeterSensorMessage : public ExpandMessageBase
{
public:
    FuelMeterSensorMessage() : ExpandMessageBase("FUEL") {};
    ~FuelMeterSensorMessage() {};
    float fuelHeight;
    int type;
    std::string raw;
    MSGPACK_DEFINE(MSGPACK_BASE(ExpandMessageBase), \
        fuelHeight, \
        type,
        raw);
};


//tpms
class TpmsSensorMessage : public ExpandMessageBase
{
#define TAH_SENSOR_NOEXIST_VALUE (255.0)
public:
    TpmsSensorMessage() : ExpandMessageBase("TPMS") {};
    ~TpmsSensorMessage() {};
    int  tyreNum;
    int  tempHiThrs;    /*温度高报警阈值, ℃*/
    float pressHiThrs;   /*压力高报警阈值, Kpa */
    float pressLoThrs;   /*压力低报警阈值, Kpa */

    std::map<int /*idx*/, float/*pressure*/>      tyrePressure;
    std::map<int /*idx*/, float/*temperature*/>   tyreTemperature;
    //msgpack::type::raw_ref  mRawData;
    MSGPACK_DEFINE(MSGPACK_BASE(ExpandMessageBase), \
        tyreNum, tempHiThrs, pressHiThrs, pressLoThrs, \
        tyrePressure, tyreTemperature);
};


class XiaMenShuoQiMsg : public ExpandMessageBase
{
public:
    XiaMenShuoQiMsg() : ExpandMessageBase("XiaMenShuoQi") {};
    ~XiaMenShuoQiMsg() {};

    uint32_t alarm_id;
    uint8_t flag_status;
    uint8_t alarm_event_type;
    uint8_t alarm_level;
    uint8_t target_speed;
    uint8_t target_obstacle_type;
    uint8_t ttc_collision_time;
    uint16_t longitudinal_relative_distance;
    uint16_t lateral_relative_distance;
    uint8_t ultrasonic_distance;
    int8_t longitudinal_relative_velocity;
    uint8_t forward_collision_warning_level;
    uint8_t lane_departure_warning;
    uint8_t left_lane_line_type;
    uint8_t right_lane_line_type;
    uint8_t device_status_fault_code;
    uint8_t aeb_brake_switch_status;
    uint8_t aeb_brake_status;
    uint16_t steering_wheel_angle;
    uint8_t steering_wheel_status;
    uint8_t gear_status;
    uint8_t accelerator_pedal_opening;
    uint8_t brake_pedal_opening;
    uint8_t vehicle_speed;
    uint16_t altitude;
    uint32_t latitude;
    uint32_t longitude;
    uint32_t ts;
    uint16_t vehicle_status;
    uint8_t alarm_identification[16];
    std::string attPath;

    MSGPACK_DEFINE(MSGPACK_BASE(ExpandMessageBase), \
        alarm_id, flag_status, alarm_event_type, alarm_level, target_speed, target_obstacle_type, ttc_collision_time, \
        longitudinal_relative_distance, lateral_relative_distance, ultrasonic_distance, longitudinal_relative_velocity, \
        forward_collision_warning_level, lane_departure_warning, left_lane_line_type, right_lane_line_type, \
        device_status_fault_code, aeb_brake_switch_status, aeb_brake_status, steering_wheel_angle, steering_wheel_status, \
        gear_status, accelerator_pedal_opening, brake_pedal_opening, vehicle_speed, altitude, latitude, longitude, ts, \
        vehicle_status, alarm_identification, attPath
    );
};


}

#endif
