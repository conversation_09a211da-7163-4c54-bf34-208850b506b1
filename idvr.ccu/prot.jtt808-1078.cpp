#include <time.h>
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <fcntl.h>
#include <termios.h>
#include <errno.h>
#include <string.h>
#include <fstream>
#include <sstream>

#include "CmdListener.h"
#include "manager.h"
#include "prot.jtt808-1078_chongqing.h"
#include "service.helper.h"
#include "ftplib.h"
#include "expand.receiver.h"
#include "json.hpp"

#include "roadnetManager.h"
#include "service.database.h"
#include <inttypes.h>
#include "jsonUtil.h"

using json = nlohmann::json;

#define PKG_VER_PATH "/system/etc/minieye/bsp.version.txt"
string getSwVer(const char * swName)
{
    char line[4 << 10];
    string v = swName;
    FILE * fp = fopen(PKG_VER_PATH, "r");

    if (fp) {
        if(NULL == fgets(line, sizeof(line), fp)){
            loge("fgets error!");
        } else {
            char * p = line + strlen(line) - 1;
            while (*p && (p > line)) {
                if ((*p != ' ') && (*p != '\t') &&
                    (*p != '\r') && (*p != '\n')) {
                    break;
                }
                *p = 0;
                p--;
            }
            v = line;
        }
        fclose(fp);
    }

    return v;
}
void string_replace(std::string &strBig, const std::string &strsrc, const std::string &strdst)
{
    std::string::size_type pos = 0;
    std::string::size_type srclen = strsrc.size();
    std::string::size_type dstlen = strdst.size();

    while ((pos = strBig.find(strsrc, pos)) != std::string::npos) {
        strBig.replace(pos, srclen, strdst);
        pos += dstlen;
    }
}

// 得到文件名
my::string get_name(const char* path)
{
    size_t len = strlen(path);

    if (len == 0) {
        return "";
    }

    std::string full_path = path;
    string_replace(full_path, "/", "\\");
    std::string::size_type iPos = full_path.find_last_of('\\') + 1;
    return full_path.substr(iPos, full_path.length() - iPos).c_str();
}

inline my::string getMedia(int i)
{
    return !i ? "V" : "v";
}


// 检查应答
int JttClient::check_res(bool jt808_new)
{
    my::string msg;
    int ret = read(msg);
    LOG_RETURN_IF(ret != 0, -1, loge, "[JttClient::check_res] failed to read msg, ret=[%d]", ret);

    // 解码
    my::constr dec(msg);
    my::string sim;
    my::ushort cmd, flag, sn;
    my::uchar  protol;

    if (jt808_new) {
        if (msg.length() < 17) {
            return false;
        }

        dec >> my::ntoh >> cmd >> flag >> protol;
        bcd2numstr(sim, dec, 10);
        dec += 10;
        dec >> sn;

    } else {

        if (msg.length() < 12) {
            return false;
        }

        // 解码cmd和flag
        dec >> my::ntoh >> cmd >> flag;

        // 解码sim
        bcd2numstr(sim, dec, 6);
        dec += 6;

        // 解码sn
        dec >> sn;
    }

    X8001 rsp;
    LOG_RETURN_IF(!rsp.decode(dec), -2, loge, "[JttClient::check_res] failed to decode: %s\n", my::hex(dec, true).c_str());
    return rsp.res == rsp.RES_OK ? 0 : -1;
}

// 检查应答
int JttClient::check_9212(bool jt808_new, X9212 & rsp)
{
    do {
        my::string msg;
        int ret = read(msg);
        logd("\n%s", my::hex(msg, true).c_str());
        LOG_RETURN_IF(ret != 0, -1, loge, "[JttClient::check_9212] failed to read msg, ret=[%d]", ret);

        // 解码
        my::constr dec(msg);
        my::string sim;
        my::ushort cmd, flag, sn;
        my::uchar  protol;

        if (jt808_new) {
            if (msg.length() < 17) {
                return -1;
            }

            dec >> my::ntoh >> cmd >> flag >> protol;
            bcd2numstr(sim, dec, 10);
            dec += 10;
            dec >> sn;

        } else {
            if (msg.length() < 12) {
                return -1;
            }

            // 解码cmd和flag
            dec >> my::ntoh >> cmd >> flag;

            // 解码sim
            bcd2numstr(sim, dec, 6);
            dec += 6;

            // 解码sn
            dec >> sn;

        }
        if (cmd != 0x9212) {
            loge("Not expect cmd 0x%04x", cmd);
            continue;
        }
        LOG_RETURN_IF(!rsp.decode(dec), -1, loge, "[JttClient::check_res] failed to decode: %s\n", my::hex(dec, true).c_str());
        break;
    } while (true);

    return rsp.res == 0 ? 0 : 1;
}

int JttClient::read(my::string& msg)
{
    bool x7e = false;
    my::string buf, tmp;

    int pos = 0, ret = 0, maxTry = 10;

    while (!x7e) {
        tmp.length(16 * 1024);
        LOG_RETURN_IF(!get(tmp), -1, loge, "[JttClient::read] failed to get msg.");
        ret = tmp.length();

        if (!ret) {
            if (maxTry-- < 0) {
                loge("[JttClient::read] failed to get msg 1, try out!");
                break;
            }

            usleep(300e3);
            continue;
        }

        for (pos = 0; pos < ret; pos++) {
            if (tmp[pos] == 0x7e) {
                x7e = true;
                break;
            }
        }
    }

    pos++;
    int e_pos = pos;
    maxTry = 10;

    while (x7e) {
        for (; e_pos < tmp.length(); e_pos++) {
            if (tmp[e_pos] == 0x7e) {
                x7e = false;
                break;
            }
        }

        buf.append((char*)tmp + pos, e_pos - pos);

        if (x7e) {
            tmp.length(16 * 1024);
            LOG_RETURN_IF(!get(tmp), -2, loge, "[JttClient::read] failed to get msg-ending.");
            e_pos = pos = 0;
            ret = tmp.length();

            if (!ret) {
                if (maxTry-- < 0) {
                    loge("[JttClient::read] failed to get msg-ending, try out!");
                    return -2;
                }

                usleep(300e3);
            }
        }
    }

    char chk = 0;
    int i = 0;

    while (i < buf.length()) {
        char c = buf[i++];

        if (c == 0x7d) {
            char cc = buf[i++];

            if (cc == 0x01) {
                c = 0x7d;

            } else if (cc == 0x02) {
                c = 0x7e;

            } else {
                return -2;
            }
        }

        msg << c;
        chk ^= c;
    }

    LOG_RETURN_IF(chk != 0, -1, loge, "[JttClient::read] failed verify chk=[%d], val=[%d]", chk, msg[i]);
    msg.length(msg.length() - 1);
    return 0;
}


// 注册
int JttClient::enregister()
{
    Manager & m = Manager::getInstance();
    X0100 req;
    req.state = m.config.sys.vehicle.province;
    req.city = m.config.sys.vehicle.city;
    req.vendor = m.config.sys.product.vendor;
    req.model = m.config.sys.product.model;
    req.device = com_service->si.id;
    req.vehicle_plate_color = (char)m.config.sys.vehicle.plate_color;
    req.vehicle_plate_num = m.config.sys.vehicle.plate_num;
    req.vin = m.config.sys.vehicle.vin;

    JttMsg t = req.encode(sim, mProtVersion);
    return send(t) ? 0 : -1;
}

// 注销
int JttClient::deregister()
{
    X0003 req;
    logd("send dereg msg!");
    JttMsg t = req.encode(sim, mProtVersion);
    return send(t) ? 0 : -1;
}

int JttClient::queryServerTime()
{
    X0004 req;
    JttMsg t = req.encode(sim, mProtVersion);
    return send(t) ? 0 : -1;
}

int JttClient::responseDriverInfo()
{
    Current st = ServiceHelper::getInstance().getStatus();
    X0702 res;
    res.state = st.ic.state;
    numstr2bcd(res.time, my::timestamp::YYMMDD_HHMMSS_S(st.ic.time), sizeof(res.time) * 2);
    res.ic_status = st.ic.ic_status;
    char name[128] = {0};
    #if 0
    my::conf::ini::CCConvert("gbk", name, sizeof(name), "UTF-8", st.ic.name.c_str(), st.ic.name.length());
    #else
    my::utf8ToGbk((char*)st.ic.name.c_str(), name);
    #endif
    res.name = name;
    char qc_code[128] = {0};
    #if 0
    my::conf::ini::CCConvert("gbk", qc_code, sizeof(qc_code), "UTF-8", st.ic.qc_code.c_str(), st.ic.qc_code.length());
    #else
    my::utf8ToGbk((char*)st.ic.qc_code.c_str(), qc_code);
    #endif
    res.qc_code = qc_code;
    char ca[128] = {0};
    #if 0
    my::conf::ini::CCConvert("gbk", ca, sizeof(ca), "UTF-8", st.ic.ca.c_str(), st.ic.ca.length());
    #else
    my::utf8ToGbk((char*)st.ic.ca.c_str(), ca);
    #endif

    res.ca = ca;
    date2bcd(st.ic.validity_date, res.validity_date);

    JttMsg msg = res.encode(sim, mProtVersion);

    if (!send(msg)) {
        logd("[0x0702] failed to send ic driver info to server.");
        return false;
    }

    return true;
}


bool JttClient::uploadEBill(my::string & ebill)
{
    X0701 r;
    r.ebillLen = ebill.length();
    my::file f;
    int len = 0;
    if (0 < (len = f.open("/data/0x8900_0xf0.log"))) {
        my::string msg(len);
        f.gets((char*)msg, len);
        f.close();
        r.ebill = msg;
    } else {
        r.ebill = ebill;
    }
    JttMsg msg = r.encode(sim, mProtVersion);
    return send(msg) ? 0 : -1;
}

// 鉴权
int JttClient::authroize()
{
    X0102 req;
    if (mbNeedAuth) {
        req.auth = com_service->si.auth;
    }
    memset(req.imei, 0, sizeof(req.imei));
    memset(req.version, 0, sizeof(req.version));
    strncpy(req.imei, com_service->si.imei.c_str(), sizeof(req.imei));
    strncpy(req.version, com_service->si.bsp_version.c_str(), sizeof(req.version));
    JttMsg t = req.encode(sim, mProtVersion);
    return send(t) ? 0 : -1;
}

// 心跳消息
int JttClient::heart_beat()
{
    JttMsg t(sim, 0x0002, mProtVersion);
    return send(t) ? 0 : -1;
}

// 位置/状态/告警上报
int JttClient::report(const Current& st, X0200 & req) {
    Manager &m = Manager::getInstance();
    if (!st.getLbsInit()) {
        return 0;
    }

    // 里程
    req.add_mileage((my::uint)(st.car.mileage * 10));

    //speed
    req.add_spd(req.lbi.speed);
    McuMsgCarInfoT carInfo;
    st.getCarInfo(&carInfo);
    if (carInfo.canFuelLeft > 0.001) {
        req.add_fuel((my::ushort)(carInfo.canFuelLeft * 10));
    }

    bool acc = st.getStateAcc();

    #if 0
    if (!mCustom.b0200NoAlarmAtt && acc) {
        // 扩展表:报警状态
        req.add_extended_status(st.getExtendAlarm());

        //扩展表:视频信号丢失报警状态
        req.add_cam_signal_status(st.getExtSignalStatus());

        //扩展表:存储器状态
        req.add_storage_status(st.getExtStorageFailStatus());
    }
    #else
    Config & config = m.config;

    // 扩展表:视频相关报警状态
    if (1 == mSpecialRecordAlarmRsp) {
        videoAlarmMask_t mask;
        mask.val = mCustom.videoAlarmMask;
        mask.set.SpecialRecordOverFlow = 0;
        req.add_extended_status(st.getExtendAlarm() & ~(mask.val));
    } else {
        req.add_extended_status(st.getExtendAlarm() & ~(mCustom.videoAlarmMask));
        if (-1 == mSpecialRecordAlarmRsp && st.getSpecialRecordOverFlow()) {
            mSpecialRecordAlarmRsp = 0;
        }
    }

    //扩展表:视频信号丢失报警状态
    videoAlarmMask_t videoAlarmMask;
    videoAlarmMask.val = mCustom.videoAlarmMask;
    if (!videoAlarmMask.set.sigalLose) {
        req.add_cam_signal_status(st.getExtSignalStatus());
    }

    //扩展表:存储器状态
    if (!videoAlarmMask.set.storageFailure) {
        req.add_storage_status(st.getExtStorageFailStatus());
    }
    #endif

    if (!acc) {
        uint32_t m = 0;
        Current::AlarmBits * ab = (Current::AlarmBits*)&m;
        ab->camFail = 1;
        m = ~m;
        req.lbi.alarm_tag &= m;
    }
    if (mCustom.alarmMask) {
        req.lbi.alarm_tag &= ~mCustom.alarmMask;
    }

    // 扩展信号位
    req.add_extern_io(st.car.ext_signal);
    // 模拟量
    req.add_analogs(st.car.ad1, st.car.ad2);
    // 无线通信网络信号强度
    req.add_wireless_intensity(st.car.wireless);
    // GNSS卫星定位数
    req.add_gnss_stars(st.car.gnss_stars);

    req.add_io_status(mSleepMode);

    ext_x0200_addition_info(st, req);

    return report(req);
}

// 位置/状态/告警上报
int JttClient::report(const Current& st)
{
    Manager &m = Manager::getInstance();
    if (!st.getLbsInit()) {
        return 0;
    }

    time_t         now = time(NULL);
    if (now < 1293811200) {
        m.mpLogger->mlog("%s, chkOnline drop invalid time data!", tag.c_str());
        return 0;
    }

    X0200 req;
    req.lbi = ServiceHelper::getInstance().getLocationBaseInfo(st);

    my::string t;
    bcd2numstr(t, req.lbi.time, sizeof(req.lbi.time));
    logd("[JttClient::report] alarm_tag=[0x%08x], lbi status=[0x%08x], lat=[%d], lng=[%d], alt=[%d], speed=[%d], dir=[%d], time=[%s]"
        "custom [%d, %d, 0x%x]",
         req.lbi.alarm_tag, req.lbi.status, req.lbi.latitude, req.lbi.longitude,
         req.lbi.height, req.lbi.speed, req.lbi.direction, t.c_str(),
         mCustom.b0200NoAlarmAtt, mCustom.bTTSPwrShort, mCustom.alarmMask);

    return report(st, req);
}

int JttClient::report(X0200& msg)
{
    Manager &m = Manager::getInstance();
    if (!m.current->getLbsInit()) {
        return 0;
    }
    time_t         now = time(NULL);
    if (now < 1293811200) {
        m.mpLogger->mlog("JttClient::report, %s, drop invalid time data!", tag.c_str());
        return 0;
    }
    JttMsg t = msg.encode(sim, mProtVersion);
    bool r = false;
    if (mProtState & AUTHORIZED) {
        r = send(t);
    }
    if (!r) {
        logd("send fail!!! %xh",mProtState);
        my::uint libTmS = my::timestamp::seconds_from_19700101();
        std::shared_ptr<minieye::AMessage> postMsg = std::make_shared<minieye::AMessage>(EVT_TYPE_PROT_DB_SAVE_RPT_DATA, shared_from_this());
        postMsg->setString("data", t.body.c_str(), t.body.length());
        postMsg->setInt32("time", libTmS);
        postMsg->post();
    }
    return r ? 0 : -1;
}
int JttClient::save(const Current& st)
{
    Manager &m = Manager::getInstance();
    if (!st.getLbsInit()) {
        return 0;
    }
    time_t         now = time(NULL);
    if (now < 1293811200) {
        m.mpLogger->mlog("JttClient::save, %s, drop invalid time data!", tag.c_str());
        return 0;
    }
    X0200 req;
    my::uint libTmS = my::timestamp::seconds_from_19700101();
    ServiceHelper &serverHelper = ServiceHelper::getInstance();
    req.lbi = serverHelper.getLocationBaseInfo(st);
    //my::string t;
    //bcd2numstr(t, req.lbi.time, sizeof(req.lbi.time));
    //logd("[JttClient::save] alarm_tag=[0x%04x], lbi status=[0x%04x], lat=[%d], lng=[%d], alt=[%d], speed=[%d], dir=[%d], time=[%s]",
    //  req.lbi.alarm_tag, req.lbi.status, req.lbi.latitude, req.lbi.longitude,
    //  req.lbi.height, req.lbi.speed, req.lbi.direction, t.c_str());

    // 里程
    req.add_mileage((my::uint)(st.car.mileage * 10));
    //speed
    req.add_spd(req.lbi.speed);
    #if 0
    if (!mCustom.b0200NoAlarmAtt) {
        // 扩展表:报警状态
        req.add_extended_status(st.getExtendAlarm());

        //扩展表:视频信号丢失报警状态
        req.add_cam_signal_status(st.getExtSignalStatus());

        //扩展表:存储器状态
        req.add_storage_status(st.getExtStorageFailStatus());
    }
    #else
    Config & config = m.config;
    // 扩展表:报警状态
    if (1 == mSpecialRecordAlarmRsp) {
        videoAlarmMask_t mask;
        mask.val = mCustom.videoAlarmMask;
        mask.set.SpecialRecordOverFlow = 0;
        req.add_extended_status(st.getExtendAlarm() & ~(mask.val));
    } else {
        req.add_extended_status(st.getExtendAlarm() & ~(mCustom.videoAlarmMask));
    }

    videoAlarmMask_t videoAlarmMask;
    videoAlarmMask.val = mCustom.videoAlarmMask;
    //扩展表:视频信号丢失报警状态
    if (!videoAlarmMask.set.sigalLose) {
        req.add_cam_signal_status(st.getExtSignalStatus());
    }
    //扩展表:存储器状态
    if (!videoAlarmMask.set.storageFailure) {
        req.add_storage_status(st.getExtStorageFailStatus());
    }
    #endif

    if (mCustom.alarmMask) {
        req.lbi.alarm_tag &= ~mCustom.alarmMask;
    }

    // 扩展信号位
    req.add_extern_io(st.car.ext_signal);
    // 无线通信网络信号强度
    req.add_wireless_intensity(st.car.wireless);
    // GNSS卫星定位数
    req.add_gnss_stars(st.car.gnss_stars);

    ext_x0200_addition_info(st, req);

    JttMsg tt = req.encode(sim, mProtVersion);

    std::shared_ptr<minieye::AMessage> msg = std::make_shared<minieye::AMessage>(EVT_TYPE_PROT_DB_SAVE_RPT_DATA, shared_from_this());
    msg->setString("data", tt.body.c_str(), tt.body.length());
    msg->setInt32("time", libTmS);
    msg->post();

    return 0;
}
bool JttClient::check_TPMS()
{
    bool bRpt = false;
    int tempHiThrs = 0;
    float tyrePressThrsHi = 0, tyrePressThrsLo = 0;
    std :: map < int, float > tyreTemps;
    std :: map < int, float > tyrePresses;
    Manager & m = Manager::getInstance();
    int32_t ret = ExpandReceiver::getInstance().getTpmsSensorMessage(tempHiThrs, tyrePressThrsHi, tyrePressThrsLo, tyreTemps, tyrePresses);
    if (!ret) {
        Current st = ServiceHelper::getInstance().getStatus();
        TpmsAlarm tpms;

        tpms.ts_ms = my::timestamp::utc_milliseconds();
        tpms.ts = tpms.ts_ms / 1000;
        tpms.speed = (my::uchar)st.getSpeed();
        tpms.evt_name = value2name("tpms", EVT_TYPE_TPMS);
        int tyreNum = 0;
        for (auto it : tyreTemps) {
            struct TyreData td;
            memset(&td, 0, sizeof(td));
            td.tyreIdx = it.first;
            auto itp = tyrePresses.find(it.first);
            if (itp == tyrePresses.end()) {
                loge("not find tyre %d press record!", it.first);
            }
            td.tyrePressure = (my::ushort)itp->second;
            td.tyreTemp = (short)it.second;
            if (td.tyrePressure && td.tyreTemp) {
                tyreNum++;
            }
            td.batteryPwr = 0;

            if (tyrePressThrsHi && td.tyrePressure && (td.tyrePressure > tyrePressThrsHi)) {
                bRpt = true;
                td.alarmBits |= (1 << 1);//胎压过高
            }
            if (tyrePressThrsLo && td.tyrePressure && (td.tyrePressure < tyrePressThrsLo)) {
                bRpt = true;
                td.alarmBits |= (1 << 2);//胎压过低
            }
            if (tempHiThrs && td.tyreTemp && (td.tyreTemp > tempHiThrs)) {
                bRpt = true;
                td.alarmBits |= (1 << 3);//温度异常
            }
            if (!td.tyrePressure || !td.tyreTemp) {
                td.alarmBits |= (1 << 4);//传感器异常
                bRpt = true;
            }
            if (!td.alarmBits) {
                td.alarmBits = 1;//周期上报
            }
            tpms.tyreData.push_back(td);
        }
        //Current::AlarmBits * pw = (Current::AlarmBits*)&m.current->alarm;
        //pw->tirePressureAlarm = bRpt;
        m.current->setAlarmtirePressure(bRpt);

        if (tyreNum && (tyreNum == tyrePresses.size())) {//周期上报
            bRpt = true;
        }
        if (bRpt) {
            tpms_report(-1, tpms);
        }
    }
    return bRpt;
}
int JttClient::check_alarm(bool rpt)
{
    AreaHelper & ah = AreaHelper::getInstance();
    ServiceHelper & sh = ServiceHelper::getInstance();
    Current st = sh.getStatus();

    X0200 loc;
    loc.lbi = sh.getLocationBaseInfo(st);

    std::vector<Area::Ptr> areas;
    int ret = ah.getAreaList(areas);
    LOG_RETURN_IF(ret != 0, -1, loge, "[JttClient::check_alarm] failed to get areas.");

    int alarm = 0;
    for (auto it = areas.begin(); it != areas.end(); ++it) {
        auto areaMap = mAreaStatus.find((*it)->id);
        if (areaMap != mAreaStatus.end()) {
            (*it)->setLocStatus(areaMap->second);
        } else {
            mAreaStatus[(*it)->id] = 0;
        }

        //logd("check area %d high speed %d", (*it)->id, (*it)->getHighSpeed());

        if ((*it)->checkAlarm(this, st, loc)) {
            alarm++;
        }

        areaMap = mAreaStatus.find((*it)->id);
        if (areaMap != mAreaStatus.end()) {
            areaMap->second = (*it)->getLocStatus();
        } else {
            mAreaStatus[(*it)->id] = (*it)->getLocStatus();
        }
    }

    if (alarm > 0) {
        int id = 0;
        DbHelper & dbh = DbHelper::getInstance();
        int ret = dbh.fetchAlarmId(id);
        if (ret == 0) {
            // 需要人工确认报警的s事件ID
            loc.lai_list[0x04] = (loc::getAlarmItem((my::ushort)id));
            /* 过检测试记录人工确认报警消息时间，避免在立即上报人工确认消息后短时间内再次触发定时上报 */
            artificialAlarmTime = (my::uint)my::timestamp::now();
            logd("artificialAlarmTime:%d, id:%d!\n", artificialAlarmTime, id);
        } else {
            logd("fetchAlarmId failed!\n");
        }
    }
    if (rpt) {
        return alarm > 0 ? report(st,loc) : 0;
    }
    return rpt;
}

// 检查待上报信息
int JttClient::check_report_data()
{
    if (!is_main_server()) {
        return 0;
    }
    // 上报数据
    DbHelper & dbh = DbHelper::getInstance();
    std::vector<ReportData> rds;
    std::vector<my::uint> ignIdTbl;
    int r = dbh.getReportDataList(rds, ignIdTbl, tag);
    LOG_RETURN_IF(r != 0, -1, loge, "Failed to get report data list.");

    if (!rds.empty()) {
        X0704 rsp;
        rsp.num = (my::ushort)rds.size();
        rsp.type = 1;
        rsp.data << my::hton;

        for (auto it = rds.begin(); it != rds.end(); ++it) {
            my::ushort len = it->data.length();
            rsp.data << len;
            rsp.data.append(it->data);
        }

        JttMsg msg = rsp.encode(sim, mProtVersion);
        logd("send report data, size=[%d]", rsp.num);

        if (send(msg)) {
            for (auto it = rds.begin(); it != rds.end(); ++it) {
                LOG_IF(dbh.delReportedData(it->id) != 0, logw, "Failed to delete report data, id=[%d]", it->id);
            }

        } else {
            logw("failed to send report data.");
            return -1;
        }
    }
    if (!ignIdTbl.empty()) {
        for (auto it = ignIdTbl.begin(); it != ignIdTbl.end(); ++it) {
            LOG_IF(dbh.delReportedData(*it) != 0, logw, "Failed to delete ignore data, id=[%d]", *it);
        }
    }
    return 0;
}

// 检查IC卡状态
int JttClient::check_iccard()
{
    Current st = ServiceHelper::getInstance().getStatus();

    //logi("check_iccard state change %d -> %d", iccard_state, st.ic.state);
    if (st.ic.seq > 0) {
        bool report = false;

        if (iccard_state != st.ic.state) {
            logi("check_iccard state change %d -> %d ic_status %d name %s qc_code %s ca %s ",
                 iccard_state, st.ic.state, st.ic.ic_status, st.ic.name.c_str(), st.ic.qc_code.c_str(), st.ic.ca.c_str());
            iccard_state = st.ic.state;
            report = true;
        }

        if (report) {
            X0702 res;
            res.state = st.ic.state;
            numstr2bcd(res.time, my::timestamp::YYMMDD_HHMMSS_S(st.ic.time), sizeof(res.time) * 2);
            res.ic_status = st.ic.ic_status;

            char name[128] = {0};
            my::utf8ToGbk((char*)st.ic.name.c_str(), name);
            res.name = name;

            char qc_code[128] = {0};
            my::utf8ToGbk((char*)st.ic.qc_code.c_str(), qc_code);
            res.qc_code = qc_code;

            char ca[128] = {0};
            my::utf8ToGbk((char*)st.ic.ca.c_str(), ca);
            res.ca = ca;
            
            date2bcd(st.ic.validity_date, res.validity_date);
            JttMsg msg = res.encode(sim, mProtVersion);

            if (!send(msg)) {
                loge("[X0702] failed to send ic driver info to server.");
                return -1;
            }
        }
    }

    return 0;
}

void JttClient::check_bgn_end_time(int alarm_type, time_t cur, time_t &bgn, time_t &end)
{
    int seek = -5;
    int duration = 10;

    bgn = cur + seek;
    end = cur + seek + duration;

    char propValue[PROP_VALUE_MAX] = {0};

    if (__system_property_get(PROP_PERSIST_808_ATT_SEEK, propValue) > 0) {
        if (propValue[0]) {
            seek = atoi(propValue);
            logd("seek = %d", seek);
            bgn = cur + seek;
        }
    }
    memset(propValue, 0, sizeof(propValue));
    if (__system_property_get(PROP_PERSIST_808_ATT_DURATION, propValue) > 0) {
        if (propValue[0]) {
            duration = atoi(propValue);
            logd("duration = %d", duration);
            end = cur + seek + duration;
        }
    }
}

void JttClient::get_attachCh(const char *algName, std::vector<int> & chVector){
    conf_t & cfg = Manager::getInstance().config.sys;
    uint32_t attachChBitTbl = 0;
    for (int i = 0; i < (cfg.cameras + cfg.ipcs); ++i){
        if(strcmp(algName, cfg.ch[i].ai.func) == 0) {
            attachChBitTbl = cfg.ch[i].ai.attachChBitTbl;
            attachChBitTbl |= (1 << i);//默认加当前通道
            break;
        }
    }

    logd("get_attachCh %s, attachChBitTbl = %x!\n", algName,  attachChBitTbl);
    if (attachChBitTbl){
        for (int i = 0; i < 32; i++){
            if (attachChBitTbl & (0x1 << i)) {
                chVector.push_back(i + 1);
            }
        }
    }
}

void JttClient::clearStoppedFtpTsk()
{
    if (mFtpTasks.size()) {
        auto it = mFtpTasks.begin();
        while (it != mFtpTasks.end()) {
            shared_ptr<JttFtpUpload> sp = it->second;
            if (!sp->working()) {
                it = mFtpTasks.erase(it);

            } else {
                it++;
            }
        }
    }
}

int JttClient::creat_dms_accessory(int ch, DmsAlarm *dms, AlarmInfoItem *alarm_info, my::string *path)
{
    int sendAtt = 1;
    Manager & m = Manager::getInstance();
    ComController::Ptr cc = m.com_controller;
    for (int i = 0; i < MAX_CLIENTS; i++) {
        ComService::Ptr cs = (*cc)[i];
        if (cs && cs->si.tag == tag.c_str()) {
            IP_CTL_BITS * bits = (IP_CTL_BITS*)&cs->si.ipCtlBits[mode];
            sendAtt = !!bits->attEnUpload;
            sendAtt &= !cs->si.is_algo_evt_att_disable("dms", dms->evt_name.c_str());
            break;
        }
    }

    if (sendAtt &&
        ((dms->level >= attSndLvl()) ||
        (dms->evt_name == "snapshotDMS") ||
        (dms->evt_name == "eyeocclusion") ||
        (dms->evt_name == "occlusion") ||
        (dms->evt_name == "mask"))) {
        int picNum = 3;
        time_t bgn = 0, end = 0;
        time_t cur = time(NULL);

        if (access("/data/no_video_att", R_OK)) {
            check_bgn_end_time(dms->event_type, cur, bgn, end);
        }

        if (dms->evt_name == "snapshotDMS") {
            bgn = end = 0;
        }

        // 生成附件
        //"[event] [sn] [ch] [storage path] [previous seconds of video] [next seconds of video] [number of pictures].\n"
        my::uint fsize = 0;
        path->assignf("/mnt/obb/mprot/%s_ch%d_%s_%02x", my::timestamp::YYYYMMDD_HHMMSS_MS(dms->ts_ms).c_str(), ch, dms->evt_name.c_str(), dms->alarm_tag.sn);
        my::file::mkdir(path->c_str());

        std::vector<int>    accessoryCh;
        get_attachCh("dms", accessoryCh);
		// 河南电信附件不打包声音
		bool mp4PackAudio = prot_subtype == "henanTelecom" ? false : true;
        if ((dms->evt_name != "snapshotDMS") && (accessoryCh.size() > 0)) {
            for (int channel = 0; channel < accessoryCh.size(); ++channel) {
                /* 生成附件消息 */
                my::string attCmd;
                int pics = (accessoryCh[channel] == ch) * picNum;
                //"[event] [sn] [ch] [storage path] [previous seconds of video] [next seconds of video] [number of pictures].\n"
                attCmd.assignf("cmd snapshot %d %d %d %s %d %d %d %d", dms->event_type, dms->alarm_tag.sn,
                                                                        accessoryCh[channel], path->c_str(), bgn, end, pics, mp4PackAudio);
                if (!LogCallProxyCmd::sendReq("media", attCmd.c_str())) {//ask media to product media files
                    loge("[JttClient::dms_report] cmd failed %s", attCmd.c_str());
                }

                logd("attCmd.c_str= %s!\n", attCmd.c_str());

                /* 附件信息填充 */
                if (access("/data/no_video_att", R_OK)) {
                    //video
                    AlarmInfoAtItem at;
                    at.path.assignf("%s/%d.mp4", path->c_str(), accessoryCh[channel]);
                    logd("at.path = %s!\n", at.path.c_str());
                    at.size = 0;
                    at.at_state = 0;
                    alarm_info->attachment.push_back(at);    // 增加附件
                    dms->alarm_tag.cnt++;
                }
                //pic
                for (int i = 0; i < pics; i++) {
                    AlarmInfoAtItem atJpg;
                    atJpg.path.assignf("%s/%d.jpg", path->c_str(), i);
                    logd("atJpg.path = %s!\n", atJpg.path.c_str());
                    atJpg.size = 0;
                    atJpg.at_state = 0;
                    alarm_info->attachment.push_back(atJpg);    // 增加附件
                    dms->alarm_tag.cnt++;
                }
            }
        }


        logd("dms.level %d, attSndLvl() %d", dms->level, attSndLvl());

        alarm_info->alarm_tag = dms->alarm_tag;      // 报警标识
    }
    return 0;
}


int JttClient::creat_hod_accessory(int ch, DmsAlarm *dms, AlarmInfoItem *alarm_info, my::string *path)
{
    int sendAtt = 1;
    Manager & m = Manager::getInstance();
    ComController::Ptr cc = m.com_controller;
    for (int i = 0; i < MAX_CLIENTS; i++) {
        ComService::Ptr cs = (*cc)[i];
        if (cs && cs->si.tag == tag.c_str()) {
            IP_CTL_BITS * bits = (IP_CTL_BITS*)&cs->si.ipCtlBits[mode];
            sendAtt = !!bits->attEnUpload;
            sendAtt &= !cs->si.is_algo_evt_att_disable("hod", dms->evt_name.c_str());
            break;
        }
    }

    if (sendAtt) {
        int picNum = 3;
        time_t bgn = 0, end = 0;
        time_t cur = time(NULL);

        if (access("/data/no_video_att", R_OK)) {
            check_bgn_end_time(dms->event_type, cur, bgn, end);
        }

        // 生成附件
        //"[event] [sn] [ch] [storage path] [previous seconds of video] [next seconds of video] [number of pictures].\n"
        my::uint fsize = 0;
        path->assignf("/mnt/obb/mprot/%s_ch%d_%s_%02x", my::timestamp::YYYYMMDD_HHMMSS_MS(dms->ts_ms).c_str(), ch, dms->evt_name.c_str(), dms->alarm_tag.sn);
        my::file::mkdir(path->c_str());

        std::vector<int>    accessoryCh;
        get_attachCh("hod", accessoryCh);
		// 河南电信附件不打包声音
		bool mp4PackAudio = prot_subtype == "henanTelecom" ? false : true;
        if (accessoryCh.size() > 0) {
            for (int channel = 0; channel < accessoryCh.size(); ++channel) {
                /* 生成附件消息 */
                my::string attCmd;
                int pics = (accessoryCh[channel] == ch) * picNum;
                //"[event] [sn] [ch] [storage path] [previous seconds of video] [next seconds of video] [number of pictures].\n"
                attCmd.assignf("cmd snapshot %d %d %d %s %d %d %d %d", dms->event_type, dms->alarm_tag.sn,
                                                                        accessoryCh[channel], path->c_str(), bgn, end, pics, mp4PackAudio);
                if (!LogCallProxyCmd::sendReq("media", attCmd.c_str())) {//ask media to product media files
                    loge("[JttClient::hod_report] cmd failed %s", attCmd.c_str());
                }

                logd("attCmd.c_str= %s!\n", attCmd.c_str());

                /* 附件信息填充 */
                if (access("/data/no_video_att", R_OK)) {
                    //video
                    AlarmInfoAtItem at;
                    at.path.assignf("%s/%d.mp4", path->c_str(), accessoryCh[channel]);
                    logd("at.path = %s!\n", at.path.c_str());
                    at.size = 0;
                    at.at_state = 0;
                    alarm_info->attachment.push_back(at);    // 增加附件
                    dms->alarm_tag.cnt++;
                }
                //pic
                for (int i = 0; i < pics; i++) {
                    AlarmInfoAtItem atJpg;
                    atJpg.path.assignf("%s/%d.jpg", path->c_str(), i);
                    logd("atJpg.path = %s!\n", atJpg.path.c_str());
                    atJpg.size = 0;
                    atJpg.at_state = 0;
                    alarm_info->attachment.push_back(atJpg);    // 增加附件
                    dms->alarm_tag.cnt++;
                }
            }
        }


        logd("dms.level %d, attSndLvl() %d", dms->level, attSndLvl());

        alarm_info->alarm_tag = dms->alarm_tag;      // 报警标识
    }
    return 0;
}

int JttClient::creat_adas_accessory(int ch, AdasAlarm *adas, AlarmInfoItem *alarm_info, my::string *path)
{
    int sendAtt = 1;
    Manager & m = Manager::getInstance();
    ComController::Ptr cc = m.com_controller;
    for (int i = 0; i < MAX_CLIENTS; i++) {
        ComService::Ptr cs = (*cc)[i];
        if (cs && cs->si.tag == tag.c_str()) {
            IP_CTL_BITS * bits = (IP_CTL_BITS*)&cs->si.ipCtlBits[mode];
            sendAtt = !!bits->attEnUpload;
            sendAtt &= !cs->si.is_algo_evt_att_disable("adas", adas->evt_name.c_str());
            break;
        }
    }

    if (sendAtt &&
        ((adas->level >= attSndLvl()) ||
        (adas->evt_name == "BLUR") ||
        (adas->evt_name == "PCW") ||
        (adas->evt_name == "FCW"))) {
        int picNum = 3;
        time_t bgn = 0, end = 0;
        time_t cur = time(NULL);

        /* adas 通道附件 */
        if (access("/data/no_video_att", R_OK)) {
            check_bgn_end_time(adas->event_type, cur, bgn, end);
        }
        if (adas->evt_name == "snapshotADAS") {
            bgn = end = 0;
        }

        //"[event] [sn] [ch] [storage path] [previous seconds of video] [next seconds of video] [number of pictures].\n"
        path->assignf("/mnt/obb/mprot/%s_ch%d_%s_%02x", my::timestamp::YYYYMMDD_HHMMSS_MS(adas->ts_ms).c_str(), ch, adas->evt_name.c_str(), adas->alarm_tag.sn);
        my::file::mkdir(path->c_str());

        /* 通道附件生成及信息填充 */
        std::vector<int>    accessoryCh;
        get_attachCh("adas", accessoryCh);
        int chCnt = accessoryCh.size();
		// 河南电信附件不打包声音
		bool mp4PackAudio = prot_subtype == "henanTelecom" ? false : true;
        if ((adas->evt_name != "snapshotADAS") && (chCnt > 0)) {
            for (int channel = 0; channel < chCnt; ++channel) {
                my::string attCmd;
                int pics = (accessoryCh[channel] == ch) * picNum;
                attCmd.assignf("cmd snapshot %d %d %d %s %d %d %d %d",
                    adas->event_type, adas->alarm_tag.sn, accessoryCh[channel], path->c_str(), bgn, end, pics, mp4PackAudio);

                if (!LogCallProxyCmd::sendReq("media", attCmd.c_str())) {//ask media to product media files
                    loge("[JttClient::adas_report] cmd failed %s", attCmd.c_str());
                } else {
                    loge("[JttClient::adas_report] cmd  %s!\n", attCmd.c_str());
                }

                /* 填充adas附件信息 */
                if (access("/data/no_video_att", R_OK)) {
                    //video
                    AlarmInfoAtItem at;
                    at.path.assignf("%s/%d.mp4", path->c_str(), accessoryCh[channel]);
                    at.size = 0;
                    at.at_state = 0;
                    alarm_info->attachment.push_back(at);    // 增加附件
                    adas->alarm_tag.cnt++;
                }
                //pic
                for (int i = 0; i < pics; i++) {
                    AlarmInfoAtItem atJpg;
                    atJpg.path.assignf("%s/%d.jpg", path->c_str(), i);
                    atJpg.size = 0;
                    atJpg.at_state = 0;
                    alarm_info->attachment.push_back(atJpg);    // 增加附件
                    adas->alarm_tag.cnt++;
                }

            }
        }

        if (adas->evt_name == "TSR") {
            /* 限速告警仅本地存储不上传至平台 */
           adas->alarm_tag.cnt = 0;
        }

        logd("adas.alarm_tag.cnt %d!\n", adas->alarm_tag.cnt);
        alarm_info->alarm_tag = adas->alarm_tag;      // 报警标识
    }
    return 0;
}



int JttClient::creat_speeding_accessory(int ch, speedingAlarm *speeding, AlarmInfoItem *alarm_info, my::string *path)
{
    int sendAtt = 1;
    Manager & m = Manager::getInstance();
    ComController::Ptr cc = m.com_controller;
    if (sendAtt) {
        int picNum = 3;
        time_t bgn = 0, end = 0;
        time_t cur = time(NULL);

        if (access("/data/no_video_att", R_OK)) {
            check_bgn_end_time(speeding->event_type, cur, bgn, end);
        }

        // 生成附件
        //"[event] [sn] [ch] [storage path] [previous seconds of video] [next seconds of video] [number of pictures].\n"
        {
            my::uint fsize = 0;
            path->assignf("/mnt/obb/mprot/%s_ch%d_%s_%02x", my::timestamp::YYYYMMDD_HHMMSS_MS(speeding->ts_ms).c_str(), 1, speeding->evt_name.c_str(), speeding->alarm_tag.sn);
            my::file::mkdir(path->c_str());
            my::string attCmd;
            attCmd.assignf("cmd snapshot %d %d %d %s %d %d %d", speeding->event_type, speeding->alarm_tag.sn, 1, path->c_str(), bgn, end, picNum);

            if (!LogCallProxyCmd::sendReq("media", attCmd.c_str())) {//ask media to product media files
                loge("[JttClient::dms_report] cmd failed %s", attCmd.c_str());
            }

            if (access("/data/no_video_att", R_OK)) {
                //video
                AlarmInfoAtItem at;
                at.path.assignf("%s/%d.mp4", path->c_str(), 1);
                logd("at.path = %s", at.path.c_str());
                at.size = 0;
                at.at_state = 0;
                alarm_info->attachment.push_back(at);    // 增加附件
                speeding->alarm_tag.cnt++;
            }
            //pic
            for (int i = 0; i < picNum; i++) {
                AlarmInfoAtItem atJpg;
                atJpg.path.assignf("%s/%d.jpg", path->c_str(), i);
                logd("atJpg.path = %s", atJpg.path.c_str());
                atJpg.size = 0;
                atJpg.at_state = 0;
                alarm_info->attachment.push_back(atJpg);    // 增加附件
                speeding->alarm_tag.cnt++;
            }
        }


        alarm_info->alarm_tag = speeding->alarm_tag;      // 报警标识
    }
    return 0;
}

int JttClient::creat_over_height_accessory(int ch, overHeightAlarm *overHeight, AlarmInfoItem *alarm_info, my::string *path)
{
    int sendAtt = 1;
    Manager & m = Manager::getInstance();
    ComController::Ptr cc = m.com_controller;
    if (sendAtt) {
        int picNum = 3;
        time_t bgn = 0, end = 0;
        time_t cur = time(NULL);

        if (access("/data/no_video_att", R_OK)) {
            check_bgn_end_time(overHeight->event_type, cur, bgn, end);
        }

        // 生成附件
        //"[event] [sn] [ch] [storage path] [previous seconds of video] [next seconds of video] [number of pictures].\n"
        {
            my::uint fsize = 0;
            path->assignf("/mnt/obb/mprot/%s_ch%d_%s_%02x", my::timestamp::YYYYMMDD_HHMMSS_MS(overHeight->ts_ms).c_str(), ch, overHeight->evt_name.c_str(), overHeight->alarm_tag.sn);
            my::file::mkdir(path->c_str());
            my::string attCmd;
            attCmd.assignf("cmd snapshot %d %d %d %s %d %d %d", overHeight->event_type, overHeight->alarm_tag.sn, ch, path->c_str(), bgn, end, picNum);

            if (!LogCallProxyCmd::sendReq("media", attCmd.c_str())) {//ask media to product media files
                loge("[JttClient::overHeight_report] cmd failed %s", attCmd.c_str());
            }

            if (access("/data/no_video_att", R_OK)) {
                //video
                AlarmInfoAtItem at;
                at.path.assignf("%s/%d.mp4", path->c_str(), 1);
                logd("at.path = %s", at.path.c_str());
                at.size = 0;
                at.at_state = 0;
                alarm_info->attachment.push_back(at);    // 增加附件
                overHeight->alarm_tag.cnt++;
            }
            //pic
            for (int i = 0; i < picNum; i++) {
                AlarmInfoAtItem atJpg;
                atJpg.path.assignf("%s/%d.jpg", path->c_str(), i);
                logd("atJpg.path = %s", atJpg.path.c_str());
                atJpg.size = 0;
                atJpg.at_state = 0;
                alarm_info->attachment.push_back(atJpg);    // 增加附件
                overHeight->alarm_tag.cnt++;
            }
        }

        alarm_info->alarm_tag = overHeight->alarm_tag;      // 报警标识
    }
    return 0;
}

int JttClient::creat_over_load_accessory(int ch, overLoadAlarm *overLoad, AlarmInfoItem *alarm_info, my::string *path)
{
    int sendAtt = 1;
    Manager & m = Manager::getInstance();
    ComController::Ptr cc = m.com_controller;
    if (sendAtt) {
        int picNum = 3;
        time_t bgn = 0, end = 0;
        time_t cur = time(NULL);

        if (access("/data/no_video_att", R_OK)) {
            check_bgn_end_time(overLoad->event_type, cur, bgn, end);
        }

        // 生成附件
        //"[event] [sn] [ch] [storage path] [previous seconds of video] [next seconds of video] [number of pictures].\n"
        {
            my::uint fsize = 0;
            path->assignf("/mnt/obb/mprot/%s_ch%d_%s_%02x", my::timestamp::YYYYMMDD_HHMMSS_MS(overLoad->ts_ms).c_str(), ch, overLoad->evt_name.c_str(), overLoad->alarm_tag.sn);
            my::file::mkdir(path->c_str());
            my::string attCmd;
            attCmd.assignf("cmd snapshot %d %d %d %s %d %d %d", overLoad->event_type, overLoad->alarm_tag.sn, ch, path->c_str(), bgn, end, picNum);

            if (!LogCallProxyCmd::sendReq("media", attCmd.c_str())) {//ask media to product media files
                loge("[JttClient::overHeight_report] cmd failed %s", attCmd.c_str());
            }

            if (access("/data/no_video_att", R_OK)) {
                //video
                AlarmInfoAtItem at;
                at.path.assignf("%s/%d.mp4", path->c_str(), 1);
                logd("at.path = %s", at.path.c_str());
                at.size = 0;
                at.at_state = 0;
                alarm_info->attachment.push_back(at);    // 增加附件
                overLoad->alarm_tag.cnt++;
            }
            //pic
            for (int i = 0; i < picNum; i++) {
                AlarmInfoAtItem atJpg;
                atJpg.path.assignf("%s/%d.jpg", path->c_str(), i);
                logd("atJpg.path = %s", atJpg.path.c_str());
                atJpg.size = 0;
                atJpg.at_state = 0;
                alarm_info->attachment.push_back(atJpg);    // 增加附件
                overLoad->alarm_tag.cnt++;
            }
        }

        alarm_info->alarm_tag = overLoad->alarm_tag;      // 报警标识
    }
    return 0;
}


int JttClient::tpms_report(int ch, TpmsAlarm & tpms)
{
#ifndef _MSC_VER
    Manager & m = Manager::getInstance();
    ServiceHelper & sh = ServiceHelper::getInstance();
    DbHelper & dh = DbHelper::getInstance();
    AlarmHelper & ah = AlarmHelper::getInstance();

    conf_t & cfg = m.config.sys;
    Current st = sh.getStatus();
    X0200 req;
    req.lbi = sh.getLocationBaseInfo(st);

    my::string t;
    bcd2numstr(t, req.lbi.time, sizeof(req.lbi.time));

    // 填写dms相关信息

    int ret = dh.fetchAlarmId(tpms.alarm_id);
    LOG_RETURN_IF(ret != 0, -1, loge, "[JttClient::%s] failed to fetch alarm id.", __FUNCTION__);
    tpms.alt = (short)st.lbs.alt;
    tpms.lat = (my::uint)(st.lbs.lat * 1000000);
    tpms.lng = (my::uint)(st.lbs.lng * 1000000);
    tpms.car_state.acc       = st.getStateAcc();
    tpms.car_state.location  = (1 == st.lbs.status);
    tpms.car_state.left_signal   = st.sensor.left_turn;
    tpms.car_state.right_signal  = st.sensor.right_turn;
    tpms.car_state.brakes        = st.sensor.brake;
    tpms.car_state.card          = st.getIcCardState();
#if 0
    memcpy((char*)tpms.alarm_tag.term_id, (const char*)com_service->si.id, com_service->si.id.length());
#else
    tpms.alarm_tag.term_id = com_service->si.id;
#endif
    tpms.alarm_tag.ts = tpms.ts;
    tpms.alarm_tag.sn = ah.fetchSn(tpms.ts);
    tpms.alarm_tag.cnt = 0;



    // 保存信息到数据库
    AlarmInfoItem alarm_info;
    alarm_info.protVer = (com_service->si.attSndProtVer < 0) ? mProtVersion : com_service->si.attSndProtVer;
    alarm_info.prot_subtype = prot_subtype;
    alarm_info.term_id = com_service->si.id;        // 终端号
    alarm_info.ts = tpms.ts;     // 时间
    alarm_info.alarm_tag = tpms.alarm_tag;       // 报警标识
    alarm_info.state = 0;
    alarm_info.channel = 0x66;
    alarm_info.event_type = 0;

    my::string path;
    int sendAtt = 1;
    ComController::Ptr cc = m.com_controller;
    for (int i = 0; i < MAX_CLIENTS; i++) {
        ComService::Ptr cs = (*cc)[i];
        if (cs && cs->si.tag == tag.c_str()) {
            IP_CTL_BITS * bits = (IP_CTL_BITS*)&cs->si.ipCtlBits[mode];
            sendAtt = !!bits->attEnUpload;
            sendAtt &= !cs->si.is_algo_evt_att_disable("bsd", "all");
            break;
        }
    }

    if (ch > 0) { //bsd.speed > mAlarmLvlSpdThrs)
        int picNum = 3;
        time_t bgn = 0, end = 0;
        time_t cur = time(NULL);
        if (access("/data/no_video_att", R_OK)) {
            check_bgn_end_time(0, cur, bgn, end);
        }
        // 生成附件
        //"[event] [sn] [ch] [storage path] [previous seconds of video] [next seconds of video] [number of pictures].\n"
        my::uint fsize = 0;
        path.assignf("/mnt/obb/mprot/%s_ch%d_%s_%02x", my::timestamp::YYYYMMDD_HHMMSS_MS(tpms.ts_ms).c_str(), ch, tpms.evt_name.c_str(), tpms.alarm_tag.sn);
        //path.assignf("/mnt/obb/mprot/%s_ch%d_%s_%02x", my::timestamp::YYYYMMDD_HHMMSS(my::uint64(bsd.ts)*1000).c_str(), ch, bsd.evt_name.c_str(), bsd.alarm_tag.sn);
        my::file::mkdir(path.c_str());

        my::string attCmd;
        attCmd.assignf("cmd snapshot %d %d %d %s %d %d %d", EVT_TYPE_TPMS, tpms.alarm_tag.sn, ch, path.c_str(), bgn, end, picNum);

        if (!LogCallProxyCmd::sendReq("media", attCmd.c_str())) {//ask media to product media files
            loge("[JttClient::%s] cmd failed %s", __FUNCTION__, attCmd.c_str());
        }

        if (access("/data/no_video_att", R_OK)) {
            //video
            AlarmInfoAtItem at;
            at.path.assignf("%s/%d.mp4", path.c_str(), ch);
            logd("at.path = %s", at.path.c_str());
            at.size = 0;
            at.at_state = 0;
            alarm_info.attachment.push_back(at);    // 增加附件
            tpms.alarm_tag.cnt++;
        }
        //pic
        for (int i = 0; i < picNum; i++) {
            AlarmInfoAtItem atJpg;
            atJpg.path.assignf("%s/%d.jpg", path.c_str(), i);
            logd("atJpg.path = %s", atJpg.path.c_str());
            atJpg.size = 0;
            atJpg.at_state = 0;
            alarm_info.attachment.push_back(atJpg);    // 增加附件
            tpms.alarm_tag.cnt++;
        }
        ComController::Ptr cc = m.com_controller;
        int sendAtt = 1;
        for (int i = 0; i < MAX_CLIENTS; i++) {
            ComService::Ptr cs = (*cc)[i];
            if (cs && cs->si.tag == tag.c_str()) {
                IP_CTL_BITS * bits = (IP_CTL_BITS*)&cs->si.ipCtlBits[mode];
                sendAtt = bits->attEnUpload;
                break;
            }
        }
        tpms.alarm_tag.cnt *= sendAtt;
        logd("tpms.alarm_tag.cnt %d, sendAtt %d", tpms.alarm_tag.cnt, sendAtt);

        alarm_info.alarm_tag = tpms.alarm_tag;      // 报警标识
    }

    logd("add alarm info to db, speed = %d\n", tpms.speed);
    ret = dh.addAlarmInfo(alarm_info);
    if (ret != 0) {
        loge("[JttClient::%s] addAlarmInfo FAIL!", __FUNCTION__);
        return -1;
    }

    /* 加入上传列表 */
    send_attachment(alarm_info);

    logd("----- tpms report ----- speed = %d\n", tpms.speed);
    req.lai_list[alarm_info.channel] = (tpms.str(mProtVersion, prot_subtype));
    auto it = alarm_info.attachment.begin();

    while (it != alarm_info.attachment.end()) {
        add2cp(it->path.c_str());
        it++;
    }

    return report(st, req);
#else
    return -1;
#endif
}


// bsd上报接口
int JttClient::bsd_report(int ch, BsdAlarm & bsd)
{
    Manager & m = Manager::getInstance();
    ServiceHelper & sh = ServiceHelper::getInstance();
    DbHelper & dh = DbHelper::getInstance();
    AlarmHelper & ah = AlarmHelper::getInstance();
#ifndef _MSC_VER
    conf_t & cfg = m.config.sys;
    Current st = sh.getStatus();
    X0200 req;
    req.lbi = sh.getLocationBaseInfo(st);

    my::string t;
    bcd2numstr(t, req.lbi.time, sizeof(req.lbi.time));

    // 填写dms相关信息

    int ret = dh.fetchAlarmId(bsd.alarm_id);
    LOG_RETURN_IF(ret != 0, -1, loge, "[JttClient::%s] failed to fetch alarm id.", __FUNCTION__);
    bsd.alt = (short)st.lbs.alt;
    bsd.lat = (my::uint)(st.lbs.lat * 1000000);
    bsd.lng = (my::uint)(st.lbs.lng * 1000000);
    bsd.car_state.acc       = st.getStateAcc();
    bsd.car_state.location  = (1 == st.lbs.status);
    bsd.car_state.left_signal   = st.sensor.left_turn;
    bsd.car_state.right_signal  = st.sensor.right_turn;
    bsd.car_state.brakes        = st.sensor.brake;
    bsd.car_state.card          = st.getIcCardState();
#if 0
    memcpy((char*)bsd.alarm_tag.term_id, (const char*)com_service->si.id, com_service->si.id.length());
#else
    bsd.alarm_tag.term_id = com_service->si.id;
#endif
    bsd.alarm_tag.ts = bsd.ts;
    bsd.alarm_tag.sn = ah.fetchSn(bsd.ts);
    bsd.alarm_tag.cnt = 0;



    // 保存信息到数据库
    AlarmInfoItem alarm_info;
    alarm_info.protVer = (com_service->si.attSndProtVer < 0) ? mProtVersion : com_service->si.attSndProtVer;
    alarm_info.prot_subtype = prot_subtype;
    alarm_info.term_id = com_service->si.id;        // 终端号
    alarm_info.ts = bsd.ts;     // 时间
    alarm_info.alarm_tag = bsd.alarm_tag;       // 报警标识
    alarm_info.state = 0;
    alarm_info.channel = 0x67;
    alarm_info.event_type = bsd.event_type;

    my::string path;
    int sendAtt = 1;
    ComController::Ptr cc = m.com_controller;
    for (int i = 0; i < MAX_CLIENTS; i++) {
        ComService::Ptr cs = (*cc)[i];
        if (cs && cs->si.tag == tag.c_str()) {
            IP_CTL_BITS * bits = (IP_CTL_BITS*)&cs->si.ipCtlBits[mode];
            sendAtt = !!bits->attEnUpload;
            sendAtt &= !cs->si.is_algo_evt_att_disable("bsd", "all");
            break;
        }
    }

    if (sendAtt) { //bsd.speed > mAlarmLvlSpdThrs)
        int picNum = 3;
        time_t bgn = 0, end = 0;
        time_t cur = time(NULL);
        if (access("/data/no_video_att", R_OK)) {
            check_bgn_end_time(bsd.event_type, cur, bgn, end);
        }
        // 生成附件
        //"[event] [sn] [ch] [storage path] [previous seconds of video] [next seconds of video] [number of pictures].\n"
        my::uint fsize = 0;
        path.assignf("/mnt/obb/mprot/%s_ch%d_%s_%02x", my::timestamp::YYYYMMDD_HHMMSS_MS(bsd.ts_ms).c_str(), ch, bsd.evt_name.c_str(), bsd.alarm_tag.sn);
        //path.assignf("/mnt/obb/mprot/%s_ch%d_%s_%02x", my::timestamp::YYYYMMDD_HHMMSS(my::uint64(bsd.ts)*1000).c_str(), ch, bsd.evt_name.c_str(), bsd.alarm_tag.sn);
        my::file::mkdir(path.c_str());

		// 河南电信附件不打包声音
		bool mp4PackAudio = prot_subtype == "henanTelecom" ? false : true;

        my::string attCmd;
        attCmd.assignf("cmd snapshot %d %d %d %s %d %d %d %d", bsd.event_type, bsd.alarm_tag.sn, ch, path.c_str(), bgn, end, picNum, mp4PackAudio);

        if (!LogCallProxyCmd::sendReq("media", attCmd.c_str())) {//ask media to product media files
            loge("[JttClient::%s] cmd failed %s", __FUNCTION__, attCmd.c_str());
        }

        if (access("/data/no_video_att", R_OK)) {
            //video
            AlarmInfoAtItem at;
            at.path.assignf("%s/%d.mp4", path.c_str(), ch);
            logd("at.path = %s", at.path.c_str());
            at.size = 0;
            at.at_state = 0;
            alarm_info.attachment.push_back(at);    // 增加附件
            bsd.alarm_tag.cnt++;
        }
        //pic
        for (int i = 0; i < picNum; i++) {
            AlarmInfoAtItem atJpg;
            atJpg.path.assignf("%s/%d.jpg", path.c_str(), i);
            logd("atJpg.path = %s", atJpg.path.c_str());
            atJpg.size = 0;
            atJpg.at_state = 0;
            alarm_info.attachment.push_back(atJpg);    // 增加附件
            bsd.alarm_tag.cnt++;
        }
        ComController::Ptr cc = m.com_controller;
        int sendAtt = 1;
        for (int i = 0; i < MAX_CLIENTS; i++) {
            ComService::Ptr cs = (*cc)[i];
            if (cs && cs->si.tag == tag.c_str()) {
                IP_CTL_BITS * bits = (IP_CTL_BITS*)&cs->si.ipCtlBits[mode];
                sendAtt = bits->attEnUpload;
                break;
            }
        }
        bsd.alarm_tag.cnt *= sendAtt;
        logd("bsd.alarm_tag.cnt %d, sendAtt %d", bsd.alarm_tag.cnt, sendAtt);

        alarm_info.alarm_tag = bsd.alarm_tag;      // 报警标识
    }

    logd("add alarm info to db, speed = %d\n", bsd.speed);
    ret = dh.addAlarmInfo(alarm_info);
    if (ret != 0) {
        loge("[JttClient::%s] addAlarmInfo FAIL!", __FUNCTION__);
        return -1;
    }

    /* 加入上传列表 */
    send_attachment(alarm_info);

    logd("----- bsd report ----- speed = %d\n", bsd.speed);
    req.lai_list[alarm_info.channel] = (bsd.str(mProtVersion, prot_subtype));
    auto it = alarm_info.attachment.begin();

    while (it != alarm_info.attachment.end()) {
        add2cp(it->path.c_str());
        it++;
    }

    if (st.getSpeed() < cfg.warn.bsd_rpt.spd_thres) {
        logd("speed less than %d, not rpt bsd to server", cfg.warn.bsd_rpt.spd_thres);
        return 0;
    }
    m.mpLogger->mlog("%s > %s", __FUNCTION__, bsd.evt_name.c_str());
    return report(st, req);
#else
    return -1;
#endif
}


// dms上报接口
int JttClient::dms_report(int ch, DmsAlarm& dms)
{
    Manager & m = Manager::getInstance();
    ServiceHelper & sh = ServiceHelper::getInstance();
    DbHelper & dh = DbHelper::getInstance();
    AlarmHelper & ah = AlarmHelper::getInstance();
    GlobalHelper & gh = GlobalHelper::getInstance();
#ifndef _MSC_VER
    Current st = sh.getStatus();
    X0200 req;
    req.lbi = sh.getLocationBaseInfo(st);

    my::string t;
    bcd2numstr(t, req.lbi.time, sizeof(req.lbi.time));

    // 填写dms相关信息

    int ret = dh.fetchAlarmId(dms.alarm_id);
    LOG_RETURN_IF(ret != 0, -1, loge, "[JttClient::dms_report] failed to fetch alarm id.");
    //dms.speed = (my::uchar)st.getSpeed();
    //dms.level = (dms.speed > mAlarmLvlSpdThrs) ? 2 : 1;
    int level = get_dms_alarm_level(dms.speed, dms.event_type, dms.ts_ms);
    if (level < 1) {
        return -1;
    }
    dms.level = (my::uchar)level;
    dms.alt = (short)st.lbs.alt;
    dms.lat = (my::uint)(st.lbs.lat * 1000000);
    dms.lng = (my::uint)(st.lbs.lng * 1000000);
    dms.car_state.acc       = st.getStateAcc();
    dms.car_state.location  = (1 == st.lbs.status);
    dms.car_state.left_signal   = st.sensor.left_turn;
    dms.car_state.right_signal  = st.sensor.right_turn;
    dms.car_state.brakes        = st.sensor.brake;
    dms.car_state.card          = st.getIcCardState();

#if 0
    memcpy((char*)dms.alarm_tag.term_id, (const char*)com_service->si.id, com_service->si.id.length());
#else
    dms.alarm_tag.term_id = com_service->si.id;
#endif
    dms.alarm_tag.ts = dms.ts;
    dms.alarm_tag.sn = ah.fetchSn(dms.ts);
    dms.alarm_tag.cnt = 0;

    // 保存信息到数据库
    AlarmInfoItem alarm_info;
    alarm_info.protVer = (com_service->si.attSndProtVer < 0) ? mProtVersion : com_service->si.attSndProtVer;
    alarm_info.prot_subtype = prot_subtype;
    alarm_info.term_id = com_service->si.id;        // 终端号
    alarm_info.ts = dms.ts;     // 时间
    alarm_info.alarm_tag = dms.alarm_tag;       // 报警标识
    alarm_info.state = 0;
    alarm_info.channel = 0x65;
    alarm_info.event_type = dms.event_type;
    my::string path;

#if 0
    int sendAtt = 1;
    ComController::Ptr cc = m.com_controller;
    for (int i = 0; i < MAX_CLIENTS; i++) {
        ComService::Ptr cs = (*cc)[i];
        if (cs && cs->si.tag == tag.c_str()) {
            IP_CTL_BITS * bits = (IP_CTL_BITS*)&cs->si.ipCtlBits[mode];
            sendAtt = !!bits->attEnUpload;
            sendAtt &= !cs->si.is_algo_evt_att_disable("dms", dms.evt_name.c_str());
            break;
        }
    }

    if (sendAtt &&
        ((dms.level >= attSndLvl()) ||
        (dms.evt_name == "snapshotDMS") ||
        (dms.evt_name == "eyeocclusion") ||
        (dms.evt_name == "occlusion") ||
        (dms.evt_name == "mask"))) {
        creat_dms_accessory(ch, &dms, &alarm_info, &path);
    }
#else
    creat_dms_accessory(ch, &dms, &alarm_info, &path);
#endif

    logd("add alarm info to db, speed = %d, level:%d, dattSndLvl:%d!\n", dms.speed, dms.level, attSndLvl());
    ret = dh.addAlarmInfo(alarm_info);
    if (ret != 0) {
        if (dms.level > 1) {
            my::file::rm(gh.getAlarmVideo(path).c_str());
        }

        loge("[JttClient::dms_report]");
        return -1;
    }
    /* 加入上传列表 */
    send_attachment(alarm_info);

    logd("----- dms report ----- speed = %d,event_type:%d!\n", dms.speed, dms.event_type);
    req.lai_list[alarm_info.channel] = (dms.str(mProtVersion, prot_subtype));
    auto it = alarm_info.attachment.begin();

    while (it != alarm_info.attachment.end()) {
        add2cp(it->path.c_str());
        it++;
    }
    m.mpLogger->mlog("%s > %s", __FUNCTION__, dms.evt_name.c_str());
    return report(st, req);
#else
    return -1;
#endif
}

// adas上报接口
int JttClient::adas_report(int ch, AdasAlarm& adas)
{
    Manager & m = Manager::getInstance();
    ServiceHelper & sh = ServiceHelper::getInstance();
    DbHelper & dh = DbHelper::getInstance();
    AlarmHelper & ah = AlarmHelper::getInstance();
    GlobalHelper & gh = GlobalHelper::getInstance();
#ifndef _MSC_VER
    Current st = sh.getStatus();
    X0200 req;
    req.lbi = sh.getLocationBaseInfo(st);

    my::string t;
    bcd2numstr(t, req.lbi.time, sizeof(req.lbi.time));


    // 填写adas相关信息

    int ret = dh.fetchAlarmId(adas.alarm_id);
    LOG_RETURN_IF(ret != 0, -1, loge, "[JttClient::adas_report] failed to fetch alarm id.");
    adas.level = get_adas_alarm_level(adas.speed, adas.event_type, adas.ts_ms);
    if (adas.level < 1) {
        return -1;
    }
    logd("----- adas report type:%d,level:%d!\n", adas.event_type, adas.level);
    if (adas.evt_name == "ldw-left") {
        adas.ldw_type = 1;
    } else if (adas.evt_name == "ldw-right") {
        adas.ldw_type = 2;
    }
    adas.alt = (short)st.lbs.alt;
    adas.lat = (my::uint)(st.lbs.lat * 1000000);
    adas.lng = (my::uint)(st.lbs.lng * 1000000);
    adas.car_state.acc      = st.getStateAcc();
    adas.car_state.location = (1 == st.lbs.status);
    adas.car_state.left_signal  = st.sensor.left_turn;
    adas.car_state.right_signal = st.sensor.right_turn;
    adas.car_state.brakes       = st.sensor.brake;
    adas.car_state.card         = st.getIcCardState();
#if 0
    memcpy((char*)adas.alarm_tag.term_id, (const char*)com_service->si.id, com_service->si.id.length());
#else
    adas.alarm_tag.term_id = com_service->si.id;
#endif
    adas.alarm_tag.ts = adas.ts;
    adas.alarm_tag.sn = ah.fetchSn(adas.ts);
    adas.alarm_tag.cnt = 0;

    // 保存信息到数据库
    AlarmInfoItem alarm_info;
    alarm_info.protVer = (com_service->si.attSndProtVer < 0) ? mProtVersion : com_service->si.attSndProtVer;
    alarm_info.prot_subtype = prot_subtype;
    alarm_info.term_id = com_service->si.id;        // 终端号
    alarm_info.ts = adas.ts;        // 时间
    alarm_info.alarm_tag = adas.alarm_tag;      // 报警标识
    alarm_info.state = 0;
    alarm_info.channel = 0x64;
    alarm_info.event_type = adas.event_type;
    my::string path;

#if 0
    int sendAtt = 1;
    ComController::Ptr cc = m.com_controller;
    for (int i = 0; i < MAX_CLIENTS; i++) {
        ComService::Ptr cs = (*cc)[i];
        if (cs && cs->si.tag == tag.c_str()) {
            IP_CTL_BITS * bits = (IP_CTL_BITS*)&cs->si.ipCtlBits[mode];
            sendAtt = !!bits->attEnUpload;
            sendAtt &= !cs->si.is_algo_evt_att_disable("adas", adas.evt_name.c_str());
            break;
        }
    }

    if (sendAtt &&
        ((adas.level >= attSndLvl()) ||
        (adas.evt_name == "BLUR") ||
        (adas.evt_name == "PCW") ||
        (adas.evt_name == "FCW"))) {
        creat_adas_accessory(ch, &adas, &alarm_info, &path);
    }
#else
    creat_adas_accessory(ch, &adas, &alarm_info, &path);
#endif

    ret = dh.addAlarmInfo(alarm_info);
    if (ret != 0) {
        if (adas.level > 1) {
            my::file::rm(gh.getAlarmVideo(path).c_str());
        }

        loge("[JttClient::adas_report]");
        return -1;
    }

    /* 加入上传列表 */
    send_attachment(alarm_info);

    logd("----- adas report ----- speed = %d, level:%d, attSndLvl:%d, mProtVersion:%d!\n", adas.speed, adas.level, attSndLvl(), mProtVersion);
    req.lai_list[alarm_info.channel] = (adas.str(mProtVersion, prot_subtype));
    auto it = alarm_info.attachment.begin();

    while (it != alarm_info.attachment.end()) {
        /* 本地存储告警附件 */
        add2cp(it->path.c_str());
        it++;
    }

    return report(st, req);
#else
    return -1;
#endif
}

// hod上报接口
int JttClient::hod_report(int ch, DmsAlarm& dms)
{
    Manager & m = Manager::getInstance();
    ServiceHelper & sh = ServiceHelper::getInstance();
    DbHelper & dh = DbHelper::getInstance();
    AlarmHelper & ah = AlarmHelper::getInstance();
    GlobalHelper & gh = GlobalHelper::getInstance();
#ifndef _MSC_VER
    Current st = sh.getStatus();
    X0200 req;
    req.lbi = sh.getLocationBaseInfo(st);

    my::string t;
    bcd2numstr(t, req.lbi.time, sizeof(req.lbi.time));

    // 填写dms相关信息

    int ret = dh.fetchAlarmId(dms.alarm_id);
    LOG_RETURN_IF(ret != 0, -1, loge, "[JttClient::dms_report] failed to fetch alarm id.");
    int level = get_dms_alarm_level(dms.speed, dms.event_type, dms.ts_ms);
    if (level < 1) {
        return -1;
    }
    dms.level = (my::uchar)level;
    dms.alt = (short)st.lbs.alt;
    dms.lat = (my::uint)(st.lbs.lat * 1000000);
    dms.lng = (my::uint)(st.lbs.lng * 1000000);
    dms.car_state.acc       = st.getStateAcc();
    dms.car_state.location  = (1 == st.lbs.status);
    dms.car_state.left_signal   = st.sensor.left_turn;
    dms.car_state.right_signal  = st.sensor.right_turn;
    dms.car_state.brakes        = st.sensor.brake;
    dms.car_state.card          = st.getIcCardState();
    dms.alarm_tag.term_id = com_service->si.id;
    dms.alarm_tag.ts = dms.ts;
    dms.alarm_tag.sn = ah.fetchSn(dms.ts);
    dms.alarm_tag.cnt = 0;

    // 保存信息到数据库
    AlarmInfoItem alarm_info;
    alarm_info.protVer = (com_service->si.attSndProtVer < 0) ? mProtVersion : com_service->si.attSndProtVer;
    alarm_info.prot_subtype = prot_subtype;
    alarm_info.term_id = com_service->si.id;        // 终端号
    alarm_info.ts = dms.ts;     // 时间
    alarm_info.alarm_tag = dms.alarm_tag;       // 报警标识
    alarm_info.state = 0;
    alarm_info.channel = 0x65;
    alarm_info.event_type = dms.event_type;
    my::string path;

    creat_hod_accessory(ch, &dms, &alarm_info, &path);

    logd("add alarm info to db, speed = %d, level:%d, dattSndLvl:%d!\n", dms.speed, dms.level, attSndLvl());
    ret = dh.addAlarmInfo(alarm_info);
    if (ret != 0) {
        if (dms.level > 1) {
            my::file::rm(gh.getAlarmVideo(path).c_str());
        }

        loge("[JttClient::dms_report]");
        return -1;
    }

    /* 加入上传列表 */
    send_attachment(alarm_info);

    logd("----- dms report ----- speed = %d,event_type:%d!\n", dms.speed, dms.event_type);
    req.lai_list[alarm_info.channel] = (dms.str(mProtVersion, prot_subtype));
    auto it = alarm_info.attachment.begin();

    while (it != alarm_info.attachment.end()) {
        add2cp(it->path.c_str());
        it++;
    }
    m.mpLogger->mlog("%s > %s", __FUNCTION__, dms.evt_name.c_str());
    return report(st, req);
#else
    return -1;
#endif
}


/* 川标超速告警上报 */
int JttClient::speeding_report(int ch, speedingAlarm& speeding) {
    Manager & m = Manager::getInstance();
    ServiceHelper & sh = ServiceHelper::getInstance();
    DbHelper & dh = DbHelper::getInstance();
    AlarmHelper & ah = AlarmHelper::getInstance();
    GlobalHelper & gh = GlobalHelper::getInstance();
#ifndef _MSC_VER
    Current st = sh.getStatus();
    X0200 req;
    req.lbi = sh.getLocationBaseInfo(st);

    my::string t;
    bcd2numstr(t, req.lbi.time, sizeof(req.lbi.time));

    logd("----- speeding report\n");
    // 填写dms相关信息
    int ret = dh.fetchAlarmId(speeding.alarm_id);
    LOG_RETURN_IF(ret != 0, -1, loge, "[JttClient::dms_report] failed to fetch alarm id.");

    speeding.alt = (short)st.lbs.alt;
    speeding.lat = (my::uint)(st.lbs.lat * 1000000);
    speeding.lng = (my::uint)(st.lbs.lng * 1000000);
    speeding.car_state.acc       = st.getStateAcc();
    speeding.car_state.location  = (1 == st.lbs.status);
    speeding.car_state.left_signal   = st.sensor.left_turn;
    speeding.car_state.right_signal  = st.sensor.right_turn;
    speeding.car_state.brakes        = st.sensor.brake;
    speeding.car_state.card          = st.getIcCardState();

#if 0
    memcpy((char*)speeding.alarm_tag.term_id, (const char*)com_service->si.id, com_service->si.id.length());
#else
    speeding.alarm_tag.term_id = com_service->si.id;
#endif
    speeding.alarm_tag.ts = speeding.ts;
    speeding.alarm_tag.sn = ah.fetchSn(speeding.ts);
    speeding.alarm_tag.cnt = 0;

    // 保存信息到数据库
    AlarmInfoItem alarm_info;
    alarm_info.protVer = (com_service->si.attSndProtVer < 0) ? mProtVersion : com_service->si.attSndProtVer;
    alarm_info.prot_subtype = prot_subtype;
    alarm_info.term_id = com_service->si.id;        // 终端号
    alarm_info.ts = speeding.ts;     // 时间
    alarm_info.alarm_tag = speeding.alarm_tag;       // 报警标识
    alarm_info.state = speeding.state;
    alarm_info.channel = 0x71;
    alarm_info.event_type = speeding.event_type;
    my::string path;

    /* 生成附件 */
    creat_speeding_accessory(ch, &speeding, &alarm_info, &path);

    logd("overspeed alarm info to db, speed = %d, dattSndLvl:%d!\n", speeding.speed, attSndLvl());
    ret = dh.addAlarmInfo(alarm_info);
    if (ret != 0) {
        my::file::rm(gh.getAlarmVideo(path).c_str());

        loge("[JttClient::overSpeed_report]");
        return -1;
    }

    /* 加入上传列表 */
    send_attachment(alarm_info);

    logd("----- overspeed report ----- speed = %d\n", speeding.speed);
    req.lai_list[alarm_info.channel] = (speeding.str(mProtVersion, prot_subtype));
    auto it = alarm_info.attachment.begin();

    while (it != alarm_info.attachment.end()) {
        add2cp(it->path.c_str());
        it++;
    }

    return report(st, req);
#else
    return -1;
#endif
}

/* 川标超过限高告警上报 */
int JttClient::over_height_report(int ch, overHeightAlarm& overHeight) {
    Manager & m = Manager::getInstance();
    ServiceHelper & sh = ServiceHelper::getInstance();
    DbHelper & dh = DbHelper::getInstance();
    AlarmHelper & ah = AlarmHelper::getInstance();
    GlobalHelper & gh = GlobalHelper::getInstance();
    RoadnetManager & rmh = RoadnetManager::getInstance();
#ifndef _MSC_VER
    Current st = sh.getStatus();
    X0200 req;
    req.lbi = sh.getLocationBaseInfo(st);

    my::string t;
    bcd2numstr(t, req.lbi.time, sizeof(req.lbi.time));

    if (!overHeight.roadLimits) {
        overHeight.carHeight = m.config.sys.carHeight;
        if (rmh.mRoadInfo.restrictInfo.heightInfo.value > 0) {
            overHeight.roadLimits = rmh.mRoadInfo.restrictInfo.heightInfo.value * 10;
        } else {
            char propValue[PROP_VALUE_MAX] = {0};
            if (__system_property_get("rw.algo.height_limit", propValue) > 0) {
                overHeight.roadLimits = atoi(propValue);
                logd("rw.algo.height_limit:%d!\n", overHeight.roadLimits);

            } else {
                overHeight.roadLimits = 3500;
            }
        }
    }

    logd("----- overHeight report ch:%d!\n", ch);
    // 填写overHeight相关信息
    int ret = dh.fetchAlarmId(overHeight.alarm_id);
    LOG_RETURN_IF(ret != 0, -1, loge, "[JttClient::overHeight_report] failed to fetch alarm id.");

    overHeight.alt = (short)st.lbs.alt;
    overHeight.lat = (my::uint)(st.lbs.lat * 1000000);
    overHeight.lng = (my::uint)(st.lbs.lng * 1000000);
    overHeight.car_state.acc       = st.getStateAcc();
    overHeight.car_state.location  = (1 == st.lbs.status);
    overHeight.car_state.left_signal   = st.sensor.left_turn;
    overHeight.car_state.right_signal  = st.sensor.right_turn;
    overHeight.car_state.brakes        = st.sensor.brake;
    overHeight.car_state.card          = st.getIcCardState();

#if 0
    memcpy((char*)overHeight.alarm_tag.term_id, (const char*)com_service->si.id, com_service->si.id.length());
#else
    overHeight.alarm_tag.term_id = com_service->si.id;
#endif
    overHeight.alarm_tag.ts = overHeight.ts;
    overHeight.alarm_tag.sn = ah.fetchSn(overHeight.ts);
    overHeight.alarm_tag.cnt = 0;

    // 保存信息到数据库
    AlarmInfoItem alarm_info;
    alarm_info.protVer = (com_service->si.attSndProtVer < 0) ? mProtVersion : com_service->si.attSndProtVer;
    alarm_info.prot_subtype = prot_subtype;
    alarm_info.term_id = com_service->si.id;        // 终端号
    alarm_info.ts = overHeight.ts;     // 时间
    alarm_info.alarm_tag = overHeight.alarm_tag;       // 报警标识
    alarm_info.state = overHeight.state;
    alarm_info.channel = 0x73;
    alarm_info.event_type = overHeight.event_type;
    my::string path;

    /* 生成附件 */
    creat_over_height_accessory(ch, &overHeight, &alarm_info, &path);

    logd("overHeight alarm info to db, speed = %d, dattSndLvl:%d!\n", overHeight.speed, attSndLvl());
    ret = dh.addAlarmInfo(alarm_info);
    if (ret != 0) {
        my::file::rm(gh.getAlarmVideo(path).c_str());

        loge("[JttClient::overHeight_report]");
        return -1;
    }
    /* 加入上传列表 */
    send_attachment(alarm_info);

    logd("----- overHeight report ----- speed = %d\n", overHeight.speed);
    req.lai_list[alarm_info.channel] = (overHeight.str(mProtVersion, prot_subtype));
    auto it = alarm_info.attachment.begin();

    while (it != alarm_info.attachment.end()) {
        add2cp(it->path.c_str());
        it++;
    }

    m.mpLogger->mlog("%s", __FUNCTION__);
    return report(st, req);
#else
    return -1;
#endif
}

/* 川标超过限高重上报 */
int JttClient::over_load_report(int ch, overLoadAlarm& overLoad) {
    Manager & m = Manager::getInstance();
    ServiceHelper & sh = ServiceHelper::getInstance();
    DbHelper & dh = DbHelper::getInstance();
    AlarmHelper & ah = AlarmHelper::getInstance();
    GlobalHelper & gh = GlobalHelper::getInstance();
    RoadnetManager & rmh = RoadnetManager::getInstance();
#ifndef _MSC_VER
    Current st = sh.getStatus();
    X0200 req;
    req.lbi = sh.getLocationBaseInfo(st);

    my::string t;
    bcd2numstr(t, req.lbi.time, sizeof(req.lbi.time));

    if (!overLoad.roadLimits) {
        overLoad.carLoad = m.config.sys.carHeight;
        if (rmh.mRoadInfo.restrictInfo.weightInfo.value > 0) {
            overLoad.roadLimits = rmh.mRoadInfo.restrictInfo.weightInfo.value * 10;
        } else {
            char propValue[PROP_VALUE_MAX] = {0};
            if (__system_property_get("rw.algo.weight_limit", propValue) > 0) {
                overLoad.roadLimits = atoi(propValue);
                logd("rw.algo.weight_limit:%d!\n", overLoad.roadLimits);

            } else {
                overLoad.roadLimits = 4500;
            }
        }
    }

    logd("----- overHeight report ch:%d!\n", ch);
    // 填写overHeight相关信息
    int ret = dh.fetchAlarmId(overLoad.alarm_id);
    LOG_RETURN_IF(ret != 0, -1, loge, "[JttClient::overLoad_report] failed to fetch alarm id.");

    overLoad.alt = (short)st.lbs.alt;
    overLoad.lat = (my::uint)(st.lbs.lat * 1000000);
    overLoad.lng = (my::uint)(st.lbs.lng * 1000000);
    overLoad.car_state.acc       = st.getStateAcc();
    overLoad.car_state.location  = (1 == st.lbs.status);
    overLoad.car_state.left_signal   = st.sensor.left_turn;
    overLoad.car_state.right_signal  = st.sensor.right_turn;
    overLoad.car_state.brakes        = st.sensor.brake;
    overLoad.car_state.card          = st.getIcCardState();

#if 0
    memcpy((char*)overLoad.alarm_tag.term_id, (const char*)com_service->si.id, com_service->si.id.length());
#else
    overLoad.alarm_tag.term_id = com_service->si.id;
#endif
    overLoad.alarm_tag.ts = overLoad.ts;
    overLoad.alarm_tag.sn = ah.fetchSn(overLoad.ts);
    overLoad.alarm_tag.cnt = 0;

    // 保存信息到数据库
    AlarmInfoItem alarm_info;
    alarm_info.protVer = (com_service->si.attSndProtVer < 0) ? mProtVersion : com_service->si.attSndProtVer;
    alarm_info.prot_subtype = prot_subtype;
    alarm_info.term_id = com_service->si.id;        // 终端号
    alarm_info.ts = overLoad.ts;     // 时间
    alarm_info.alarm_tag = overLoad.alarm_tag;       // 报警标识
    alarm_info.state = overLoad.state;
    alarm_info.channel = 0x72;
    alarm_info.event_type = overLoad.event_type;
    my::string path;

    /* 生成附件 */
    creat_over_load_accessory(ch, &overLoad, &alarm_info, &path);

    logd("overLoad alarm info to db, speed = %d, dattSndLvl:%d!\n", overLoad.speed, attSndLvl());
    ret = dh.addAlarmInfo(alarm_info);
    if (ret != 0) {
        my::file::rm(gh.getAlarmVideo(path).c_str());

        loge("[JttClient::overHeight_report]");
        return -1;
    }
    /* 加入上传列表 */
    send_attachment(alarm_info);

    logd("----- overHeight report ----- speed = %d\n", overLoad.speed);
    req.lai_list[alarm_info.channel] = (overLoad.str(mProtVersion, prot_subtype));
    auto it = alarm_info.attachment.begin();

    while (it != alarm_info.attachment.end()) {
        add2cp(it->path.c_str());
        it++;
    }

    m.mpLogger->mlog("%s", __FUNCTION__);
    return report(st, req);
#else
    return -1;
#endif
}


// 车辆监测系统上报接口
int JttClient::vms_report(int ch, VmsAlarm& vms)
{
    return -1;
}


// 获取服务器信息
ComService::ServerInfo JttClient::get_server_info()
{
    return com_service ? com_service->si : ComService::ServerInfo();
}

JttClient& JttClient::set_com_service(ComService* com_service)
{
    this->com_service = com_service;
    mProtVersion = com_service->protVersion();
    return *this;
}

const my::string& JttClient::get_tag() const
{
    return tag;
}

// 通用应答
bool JttClient::reply(const my::string& sim, my::ushort cmd, my::ushort sn, char res)
{
    JttMsg msg = X0001::encode(sim, cmd, sn, res, mProtVersion);
    return send(msg);
}

void JttClient::onProtMsg(const my::string& sim, my::ushort cmd, my::ushort sn, const my::constr& data)
{
    Manager & m = Manager::getInstance();

    mLastMsgRcvTs = my::timestamp::now();
    setProtRcvTimeout(PROT_REC_TIMEOUT_DEFAULT); /* 收到消息后设置接收超时时间为180s */

    if (mbDummy) {
        logd("dummy");
        return;
    }
    if (!checkRcvCmdFilter(cmd)) {
        logd("cmd 0x%04x not find in filter!", cmd);
        return;
    }

    if (!is_main_server()) {
        switch (cmd) {
            case 0x8100:
            case 0x8001:
            //case 0x8104:
                break;

            default:
                logw("[JttClient::onProtMsg] secondary server sim=[%s], cmd=[0x%04x], sn=[%d], data=[%d]\n%s",
                     sim.c_str(), cmd, sn, data.length(), my::hex(data, true).c_str());
                return;

        }
    }

    if ((cmd != 0x8103) && !access("/data/dump_jtt808_rcv2file", R_OK) && m.mpLogger) {
        my::string hex = my::hex(data, true);
        m.mpLogger->mlog("0x%04x", cmd);
        m.mpLogger->mlog_raw(hex.c_str(), hex.length());
    }

    logd("[JttClient::onProtMsg] sim=[%s], cmd=[0x%04x], sn=[%d], data=[%d]\n%s", sim.c_str(), cmd, sn, data.length(), my::hex(data, true).c_str());

    switch (cmd) {
        case 0x8100:
            proc8100(sim, sn, data);
            break; // 注册应答

        case 0x8001:
            proc8001(sim, sn, data);
            break; // 通用应答

        case 0x8004: {
            m.mpLogger->mlog("Server time : %02x-%02x-%02x %02x:%02x:%02x\n",
                (my::uchar)data[0], (my::uchar)data[1], (my::uchar)data[2],
                (my::uchar)data[3], (my::uchar)data[4], (my::uchar)data[5]);
            break;
        }

        case 0x8103:
            proc8103(sim, sn, data);
            break; // 设置终端参数

        case 0x8104:
            proc8104(sim, sn, data);
            break; // 查询终端参数

        case 0x8105:
            proc8105(sim, sn, data);
            break; // 查询终端参数

        case 0x8106:
            proc8106(sim, sn, data);
            break; // 查询终端的指定参数

        case 0x8107:
            proc8107(sim, sn, data);
            break; // 查询终端参数

        case 0x8108:
            proc8108(sim, sn, data);
            break; // 终端升级

        case 0x8201:
            proc8201(sim, sn, data);
            break; // 位置信息查询

        case 0x8202:
            proc8202(sim, sn, data);
            break; // 临时位置跟踪控制

        case 0x8203:
            proc8203(sim, sn, data);
            break; // 清除告警标志

        case 0x8204: {//链路检测
            // 回复通用应答
            reply(sim, 0x8204, sn, 0);
            break;
        }

        case 0x8300:
            proc8300(sim, sn, data);
            break; // 文本信息下发

        case 0x8302:
            proc8302(sim, sn, data);
            break; // 提问下发

        case 0x8303:
            proc8303(sim, sn, data);
            break; // 信息点播菜单设置

        case 0x8304:
            proc8304(sim, sn, data);
            break; // 信息服务

        case 0x8401:
            proc8401(sim, sn, data);
            break; // 设置电话本

        case 0x8500:
            proc8500(sim, sn, data);
            break; // 车辆控制

        case 0x8600:
            proc8600(sim, sn, data);
            break; // 设置圆形区域

        case 0x8601:
            proc8601(sim, sn, data);
            break; // 删除圆形区域

        case 0x8602:
            proc8602(sim, sn, data);
            break; // 设置矩形区域

        case 0x8603:
            proc8603(sim, sn, data);
            break; // 删除矩形区域

        case 0x8604:
            proc8604(sim, sn, data);
            break; // 设置多边形区域

        case 0x8605:
            proc8605(sim, sn, data);
            break; // 删除多边形区域

        case 0x8606:
            proc8606(sim, sn, data);
            break; // 设置路线

        case 0x8607:
            proc8607(sim, sn, data);
            break; // 删除路线

        case 0x8608:
            proc8608(sim, sn, data);
            break; // 查询

        case 0x8701:
            proc8701(sim, sn, data);
            break;

        case 0x8702:
            proc8702(sim, sn, data);
            break; // 上报驾驶员身份信息请求

        case 0x8800:
            proc8800(sim, sn, data);
            break; // 多媒体数据上传应答

        case 0x8801:
            proc8801(sim, sn, data);
            break; // 抓取图片

        case 0x8802:
            proc8802(sim, sn, data);
            break; // 存储多媒体数据检索

        case 0x8803:
            proc8803(sim, sn, data);
            break; // 存储多媒体数据上传命令

        case 0x8804:
            proc8804(sim, sn, data);
            break; // 录音命令

        case 0x8805:
            proc8805(sim, sn, data);
            break; // 单条存储多媒体数据检索上传命令

        case 0x8900:
            proc8900(sim, sn, data);
            break;// 苏标设备信息查询

        case 0x9003:
            proc9003(sim, sn, data);
            break;

        case 0x9101:
            proc9101(sim, sn, data);
            break; // 播放实时音视频

        case 0x9102:
            proc9102(sim, sn, data);
            break; // 音视频实时传输控制

        case 0x9105:
            proc9105(sim, sn, data);
            break; // 实时音视频传输状态通知

        case 0x9201:
            proc9201(sim, sn, data);
            break; // 平台下发远程录像回放请求

        case 0x9202:
            proc9202(sim, sn, data);
            break; // 平台下发远程录像回放控制

        case 0x9205:
            proc9205(sim, sn, data);
            break; // 查询资源列表（历史音视频查询）

        case 0x9206:
            proc9206(sim, sn, data);
            break; // 文件上传指令

        case 0x9207:
            proc9207(sim, sn, data);
            break; // 文件上传指令

        case 0x9208:
            proc9208(sim, sn, data);
            break; // 报警附件上传指令
        
        case 0x9212:
            proc9212(sim, sn, data);
            break; // 文件上传完成消息应答

        case 0x8700:
            proc8700(sim, sn, data);
            break;

        default:
            if (!ext_proc(sim, cmd, sn, data)) {
                // 回复通用应答: 不支持
                reply(sim, cmd, sn, access("/data/notImplRptEC", R_OK) ? 0 : 3);
                logw("[JttTcpClient::proc] unkown message: sim=[%s], cmd=[0x%04x], sn=[%d], data=[%d]\n%s", sim.c_str(), cmd, sn, data.length(), my::hex(data, true).c_str());
            }
    }
}
// 通用应答
bool JttClient::proc8001(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8001 rsp;

    if (!rsp.decode(data)) {
        logw("[0x8001] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        return false;
    }

    logd("[0x8001] Server response: cmd=[0x%04x], sn=[%d], res=[%d].", rsp.cmd, rsp.sn, rsp.res);

    JttMsgTimer::Item item =  timer.ack(rsp.cmd, rsp.sn);

    switch (rsp.cmd) {
        case 0x0102: { // 鉴权应答
                if (!rsp.res) {
                    logi("[0x8001] Terminal is authoreized to login.");
                    mProtState |= JttClient::AUTHORIZED;
                    expiries[2] = (my::uint)my::timestamp::now() + timeout[2];
                } else {
                    loge("[0x8001] Terminal is authoreized fail!");
                    com_service->si.auth = "";
                    mProtState = JttClient::INVALID;
                    expiries[0] = 0;
                    expiries[1] = 0;
                    resetAuthed();
                }
            }
            break;

        case 0x0002: { // 心跳应答
                logd("[0x8001] Heart-beat ack: sn=[0x%04x].", rsp.sn);
            }
            break;

        case 0x0200: {
                if (rsp.res) {
                    loge("[0x8001] for 0200, res = %d", rsp.res);
                    if (4 == rsp.res) {
                        /* 部标过检机构的检测软件在收到进出线路人工报警后，下发了0x8001消息 */
                        Manager & m = Manager::getInstance();
                        logd("[0x8001] for 0200, res = %d", rsp.res);
                        if (m.current->getAlarmPathStatus()) {
                            m.current->setAlarmPathStatus(false);
                        }
                        /* 已发送特殊报警录像达到阈值报警 */
                        if (!mSpecialRecordAlarmRsp) {
                            mSpecialRecordAlarmRsp = 1;
                        }
                    }
                } else {
                    Manager & m = Manager::getInstance();
                    if (m.current->getAlarmPathStatus()) {
                        /* 部标过检机构的检测软件在收到进出线路人工报警后，下发了0x8001消息 */
                        logd("[0x8001] for 0200, res = %d", rsp.res);
                        m.current->setAlarmPathStatus(false);
                    }
                }
            }
            break;

        default: {
                return ext_procX8001(rsp);
            }
    }

    return true;
}
bool JttClient::proc8003(const my::string& sim, my::ushort sn, const my::constr& data)
{
    loge("todo .....................................");
    return true;
}
// 注册应答
bool JttClient::proc8100(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8100 rsp;

    if (!rsp.decode(data)) {
        logw("[0x8100] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        return false;
    }

    logd("[0x8100] Register response: sn=[%d], res=[%d], auth=[%s].", rsp.sn, rsp.res, rsp.auth.c_str());

    // 发起登录请求
    if (rsp.res == 0) {
        mProtState |= JttClient::JttREGISTERED;
        expiries[1] = (my::uint)my::timestamp::now() + timeout[1];
        com_service->si.auth = rsp.auth; // 保存鉴权码

        bool stat = false;
        char resp[64] = {0};
        char tmp[256] = {0};
        char cmd[512] = {0};
        snprintf(tmp, sizeof(tmp), "cmd saveconf %s %s %s", this->tag.c_str(), "auth", com_service->si.auth.c_str());
        my::gbkToUtf8(tmp, cmd);
        stat = LogCallProxyCmd::sendReq("config", cmd, resp, sizeof(resp), 1);
        LOG_RETURN_IF(stat != true, false, loge, "failed to save %s auth, cmd=[%s]", this->tag.c_str(), cmd);
    }

    return true;
}

// 设置终端参数
bool JttClient::proc8103(const my::string& sim, my::ushort sn, const my::constr& data)
{
    Manager & m = Manager::getInstance();
    ServiceHelper & sh = ServiceHelper::getInstance();
    X8103 req;

    if (!req.decode(data)) {
        logw("[0x8103] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        return false;
    }

    logi("[0x8103] num=[%d]", req.list.size());

    int ret = 0;
    int mode = 0;
    ComService::ServerInfo& si = this->com_service->si;

    m.mpLogger->mlog("%s > data = %s\n", __FUNCTION__, my::hex(data).c_str());
    ret = sh.setConfig(this, req.list, si, mode);
    LOG_IF(ret != 0, logw, "[0x8103] failed to set config, ret=[%d]", ret);

    reply(sim, 0x8103, sn, ret == 0 ? 0 : 1);

    if (!ret && mode) {
        m.state_controller->sg.reload_service(this->com_service->shared_from_this(), si);
        logi("[0x8103] reload servcie, tag=[%s]", this->get_tag().c_str());
    }

    return true;
}

// 查询终端参数
bool JttClient::proc8104(const my::string& sim, my::ushort sn, const my::constr& data)
{
    ServiceHelper & sh = ServiceHelper::getInstance();
    X8104 req;

    if (!req.decode(data)) {
        logw("[0x8104] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        return false;
    }

    logd("[0x8104] receive 0x8104 cmd.");
    X0104 rsp;
    rsp.sn = sn;

    int ret = sh.getConfig(this, rsp.list);

    JttMsg msg = rsp.encode(sim, mProtVersion);

    if (!send(msg)) {
        loge("[0x8104] failed to send response, sn=[%d]", sn);
        return false;
    }

    return true;
}



static bool parseFtpUrl(my::string & url, my::string & addr, my::string & usrPwd)
{
    char * p = (char*)url.c_str();
    char * q = strchr(p, '@');
    if (!q) {
        return false;
    }
    my::string tmp = "ftp://";
    p += 6;//skip ftp://

    *q = 0;//end pwd
    tmp.append(q + 1);

    usrPwd = p;

    addr = tmp.c_str();
    logd("addr %s", addr.c_str());
    return true;
}
// 升级
bool JttClient::proc8105(const my::string& sim, my::ushort sn, const my::constr& data)
{
    Manager & m = Manager::getInstance();
    X8105 req;

    if (prot_subtype == "sichuan") {
        if (!req.decode(prot_subtype, data)) {
            logw("[0x8105] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
            return false;
        }
    } else {
        if (!req.decode(data)) {
            logw("[0x8105] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
            return false;
        }
    }

    char prop[PROP_VALUE_MAX] = {0};
    if (__system_property_get(PROP_RW_MINIEYE_ENABLE_UPGRADE, prop) > 0) {
        if (prop[0] && !!strcmp(prop, prot_subtype.c_str())) {
            logd("disable upgrade! %s", prop);
            reply(sim, 0x8105, sn, 3);
            return false;
        }
    }
    logd("[8105]req.cmd:%d!\n", req.cmd);
    int ret = 0;
    bool reboot = false;
    if (0 == req.cmd) {
        ret = 0; /*for test*/
    } else if (1 == req.cmd) {
        string cmdSz = "curl -v --connect-timeout 10 -m 1800 -L ";
        my::string addr, usrpwd;
        if (parseFtpUrl(req.ctx[0], addr, usrpwd)) {
            cmdSz += " -u '";
            cmdSz += usrpwd;
            cmdSz += "' ";
            cmdSz += addr;
        } else {
            cmdSz += req.ctx[0];
        }
        #if 0
        cmdSz += " -o /data/bsp.mpk 2>&1; [ $? == 0 ] && "
                 "cd /data/ && install_mpk.sh bsp.mpk";
        #else
        cmdSz += " -o /data/bsp.tar.gz 2>&1; [ $? == 0 ] && "
                 "cd / && mount -o rw,remount /system; busybox tar xzvf /data/bsp.tar.gz";
        #endif
        logd("CMD : %s", cmdSz.c_str());
        char oldVer[256];
        snprintf(oldVer, sizeof(oldVer), "%s", getSwVer().c_str());
        logd("oldVer %s\n", oldVer);
        m.ttsUtf8("开始升级");
        FILE * fp = popen(cmdSz.c_str(), "r");
        if (fp) {
            char cmdPrint[4 << 10];
            while (fgets(cmdPrint, sizeof(cmdPrint), fp)) {
                logd("ftpUpgrade: %s", cmdPrint);
            }
            pclose(fp);
        }
        char newVer[256] = {0};
        snprintf(newVer, sizeof(newVer), "%s", getSwVer().c_str());
        logd("newVer %s\n", newVer);
        reboot = !!strcmp(oldVer, newVer);
        ret = !reboot;
        m.ttsUtf8(reboot ? "升级成功,重启中" : "升级失败");

        X0108 rsp;
        JttMsg msg = rsp.encode(sim, !!ret, mProtVersion);
        if (!send(msg)) {
            loge("[0x8108] failed to send response 0x0108, sn=[%d]", sn);
            return false;
        }
    } else if (2 == req.cmd) {
        ret = 3;
    }else if (3 == req.cmd || 4 == req.cmd || 5 == req.cmd) {
        reboot = true;
        ret = 0;
    } else if (6 == req.cmd || 7 == req.cmd) {
        dummy(true);
        ret = 0;
    } else if (8 == req.cmd) {
        /* 通天星扩展为休眠唤醒 */
        if (prot_subtype == "sichuan") {
            ret = ext_procX8105(req);
        } else {
            ret = 3;
        }
    }else {
        ret = 3;
    }
    reply(sim, 0x8105, sn, ret);

    if (reboot) {
        sleep(3);
        system("reboot");
    }
    return true;
}

// 查询指定终端参数
bool JttClient::proc8106(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8106 req;

    if (!req.decode(data)) {
        logw("[0x8106] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        return false;
    }
    int count = 0;
    for (auto i : req.list) {
        if (i == 0xff00) { //get mac twice for sanbao
            count = 1;
            break;
        }
    }
    logi("[0x8106] query para size=[%d]", req.num);
    do {
        X0104 rsp;
        rsp.sn = sn;
        ServiceHelper & sh = ServiceHelper::getInstance();
        int ret = sh.getConfig(this, req.list, rsp.list);

        JttMsg msg = rsp.encode(sim, mProtVersion);

        if (!send(msg)) {
            loge("[0x8106] failed to send response, sn=[%d]", sn);
            return false;
        }
    } while(count--);
    return true;
}

// 查询版本信息
bool JttClient::proc8107(const my::string& sim, my::ushort sn, const my::constr& data)
{
    Manager & m = Manager::getInstance();
    Config & cfg = m.config;
    X0107 rsp;
    string version = getSwVer();
    my::string imei, iccid;
    getSimInfo(imei, iccid);
    rsp.vendor = cfg.sys.product.vendor;
    rsp.product = cfg.sys.product.model;
    JttMsg msg = rsp.encode(sim, iccid, version, com_service->si.id.c_str(), mProtVersion);

    if (!send(msg)) {
        loge("[0x8107] failed to send response, sn=[%d]", sn);
        return false;
    }

    return true;
}
bool JttClient::proc8108(const my::string& sim, my::ushort sn, const my::constr& data)
{
    Manager & m = Manager::getInstance();
    X8108 req;

    if (!req.decode(data, mProtVersion)) {
        logw("[0x8108] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        return false;
    }

    logd("[0x8108] type %d, ver{%d} = %s, pkgLen %d\n", req.type, req.verLen, req.version.c_str(), req.pkgLen);
    char prop[PROP_VALUE_MAX] = {0};
    if (__system_property_get(PROP_RW_MINIEYE_ENABLE_UPGRADE, prop) > 0) {
        if (prop[0] && !!strcmp(prop, prot_subtype.c_str())) {
            logd("disable upgrade! %s", prop);
            reply(sim, 0x8108, sn, 3);
            return false;
        }
    }

    int ret = 0;
    bool reboot = false;
    if (0 == req.type) {
        FILE * fp = fopen("/data/bsp.tar.gz", "w");

        if (fp) {
            if (req.pkgLen != fwrite(req.pkgData, 1, req.pkgLen, fp)) {
                loge("pkg write fail!\n");
            }

            fclose(fp);
        }

        m.ttsUtf8("开始升级");
        string cmdSz = "cd / && mount -o rw,remount /system; busybox tar xzvf /data/bsp.tar.gz";
        logd("CMD : %s", cmdSz.c_str());
        char oldVer[256];
        snprintf(oldVer, sizeof(oldVer), "%s", getSwVer().c_str());
        logd("oldVer %s\n", oldVer);
        fp = popen(cmdSz.c_str(), "r");
        if (fp) {
            char cmdPrint[4 << 10];
            while (fgets(cmdPrint, sizeof(cmdPrint), fp)) {
                logd("ftpUpgrade: %s", cmdPrint);
            }
            pclose(fp);
        }
        char newVer[256] = {0};
        snprintf(newVer, sizeof(newVer), "%s", getSwVer().c_str());
        logd("newVer %s\n", newVer);
        reboot = !!strcmp(oldVer, newVer);
        ret = !reboot;
        m.ttsUtf8(reboot ? "升级成功,重启中" : "升级失败");
    } else {
        ret = 3;
    }
#if 0
    JttMsg msg0(sim, 0x0005, mProtVersion);
    my::string& body = msg0.body;
    my::ushort repeatNum = 0;
    body << my::hton << sn << repeatNum;

    if (!send(msg0)) {
        loge("[0x8108] failed to send response 0x0005, sn=[%d]", sn);
        return false;
    }
#endif
    if (reboot) {
#if 1
        std::string cmd = "touch /data/minieye/";
        cmd += tag + "_upgrade_ok; sync";
        system(cmd.c_str());
#else
        X0108 rsp;
        JttMsg msg = rsp.encode(sim, ret, mProtVersion);
        if (!send(msg)) {
            loge("[0x8108] failed to send response 0x0108, sn=[%d]", sn);
            return false;
        }
#endif
        sleep(3);
        system("reboot");
    }
    return true;
}

// 位置信息查询
bool JttClient::proc8201(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8201 req;

    if (!req.decode(data)) {
        logw("[0x8201] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        return false;
    }

    logi("[0x8201] received.");

    X0201 rsp;
    rsp.sn = sn;
    Current st = ServiceHelper::getInstance().getStatus();
    rsp.loc.lbi = ServiceHelper::getInstance().getLocationBaseInfo(st);
    my::string t;
    bcd2numstr(t, rsp.loc.lbi.time, sizeof(rsp.loc.lbi.time));

    // 里程
    rsp.add_mileage((my::uint)(st.car.mileage * 10));
    JttMsg msg = rsp.encode(sim, mProtVersion);
    if (!send(msg)) {
        loge("[0x8201] failed to send msg, sn=[%d]", rsp.sn);
        return false;
    }

    return true;
}
// 临时位置跟踪控制
bool JttClient::proc8202(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8202 req;

    if (!req.decode(data)) {
        logw("[0x8202] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回复通用应答: 消息有错
        reply(sim, 0x8202, sn, 2);
        return false;
    }

    logi("[0x8202] interval=[%d], expires=[%d]", req.interval, req.expires);
    timeout[5] = req.interval;
    expiries[5] = 0;
    trace_expire = (timeout[5] == 0) ? 0 : (my::uint)my::timestamp::now() + req.expires;
    reply(sim, 0x8202, sn, 0);
    return true;
}

// 告警标志位清除
bool JttClient::proc8203(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8203 req;

    if (!req.decode(data)) {
        logw("[0x8203] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回复通用应答: 消息有错
        reply(sim, 0x8203, sn, 2);
        return false;
    }

    logi("[0x8203] sn=[%d], warnBits=[%xh]", req.sn, req.warnBits);

    if (is_main_server()) { // 清除标记位
        Manager & m = Manager::getInstance();

        if (getBit(req.warnBits, 0)) {
            m.current->setAlarmEmergency(false);
        }

        if (getBit(req.warnBits, 3)) {
            m.current->setAlarmDangerous(false);
        }

        if (getBit(req.warnBits, 20)) {
            m.current->setAlarmAreaStatus(false);
        }

        if (getBit(req.warnBits, 21)) {
            m.current->setAlarmPathStatus(false);
        }

        if (getBit(req.warnBits, 22)) {
            m.current->setAlarmPathTimeFault(false);
        }

        if (getBit(req.warnBits, 27)) {
            m.current->setAlarmIllegalLauch(false);
        }

        if (getBit(req.warnBits, 28)) {
            m.current->setAlarmIllegalMove(false);
        }

        if (getBit(req.warnBits, 31)) {
            m.current->setAlarmIllegalDoorOpen(false);
        }
    }

    reply(sim, 0x8203, sn, 0);
    return true;
}


// 文本信息下发
bool JttClient::proc8300(const my::string& sim, my::ushort sn, const my::constr& data)
{
    Manager & m = Manager::getInstance();
    X8300 req;
    char  ret = 2;

    if (!req.decode(data, mProtVersion)) {
        loge("[0x8300] Failed to decode, msg=\n");
        my::hexdump(data, true);

        // 回复通用应答: 消息有错
        reply(sim, 0x8300, sn, 2);
        return false;
    }

    logi("[0x8300] TTS received: cmd=[%02x], msg=[%s]", req.cmd, my::hex(req.msg).c_str());

    if (req.cmd & 0x08) {
        /* 移除tts文本信息中的空格否则tts语音播报和液晶屏显示会出现信息不全 */
        req.msg = req.msg.replace(' ', true, ',');
        /* 语音播报 */
        m.ttsUtf8(req.msg);

        reply(sim, 0x8300, sn, 0);

        char propValue[PROP_VALUE_MAX] = {0};

        __system_property_get(PROP_RW_MINIEYE_808TTS_IDX, propValue);
        int propIdx = atoi(propValue);
        propIdx %= 10;
        snprintf(propValue, sizeof(propValue), "%d", propIdx + 1);
        __system_property_set(PROP_RW_MINIEYE_808TTS_IDX, propValue);

        char msgGbk[1024] = {0};
        my::utf8ToGbk((char*)req.msg.c_str(), msgGbk);
        std::string recordTTS = "cmd show_message 0 ";
        vector<char> resp;
        LogCallProxyCmd::sendReq("hostio", recordTTS.c_str(), resp);
        recordTTS = "cmd show_message 8 '";
        recordTTS += msgGbk;
        recordTTS += "'";
        logd("%s", recordTTS.c_str());
        resp.clear();
        LogCallProxyCmd::sendReq("hostio", recordTTS.c_str(), resp);

        std::string msgfileName = "/data/tts/";
        msgfileName += propValue;
        msgfileName += ".gbk";
        my::file msgfile;
        msgfile.mkdir("/data/tts");
        if (msgfile.open(msgfileName.c_str()) >= 0) {
            recordTTS = my::timestamp::YYYYMMDD_HHMMSS() + " : ";
            recordTTS += msgGbk;
            msgfile.puts(recordTTS.c_str(), recordTTS.length());
            msgfile.close();
        }
        ret = 0;
    }

    if (req.cmd & 0x04) {
        //char cmd[256] = {0};
        my::string cmd;
        cmd.append("cmd setUiTips");
        cmd += " ";
        if (req.cmd & 0x1) {
            cmd += "紧急:";
        }
        cmd += req.msg + " ";
        /* 字体颜色 */
        cmd += "16777215";
        loge("%s", cmd.c_str());
        if (!LogCallProxyCmd::sendReq("media", cmd.c_str())) {
            ret = 0;
        }
        ret = 0;
    }
    reply(sim, 0x8300, sn, ret);
    return true;
}

// 提问下发
bool JttClient::proc8302(const my::string& sim, my::ushort sn, const my::constr& data)
{
    Manager & m = Manager::getInstance();
    X8302 req;

    if (!req.decode(data)) {
        logw("[0x8600] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回通用应答：消息有错
        reply(sim, 0x8302, sn, 2);
        return false;
    }

#if 1
    // todo: media
    // 回通用应答：不支持
    reply(sim, 0x8302, sn, 2);
#else

    if (req.flag & 0x08) {
        my::string txt;
        txt << my::hton << (my::ushort)0xCECA << (my::ushort)0xCCE2 << req.question << "."; // 问题

        for (auto it = req.answers.begin(); it != req.answers.end(); ++it) {
            txt << (my::ushort)0xB4F0 << (my::ushort)0xB0B8;
            txt.appendf("%d  ", it->id + 1);
            txt << it->ans << ".";
        }

        m.text2speach(txt, true);
        X0302 rsp;
        rsp.sn = sn;
        rsp.ans_id = req.answers.empty() ? 0 : req.answers.begin()->id;
        JttMsg msg = rsp.encode(sim);

        if (!send(msg)) {
            loge("[0x8600] Failed to send answers.");
            return false;
        }

    } else {
        reply(sim, 0x8302, sn, 2);

    }

#endif
    return true;
}

// 信息点播菜单设置
bool JttClient::proc8303(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8303 req;

    if (!req.decode(data)) {
        logw("[0x8303] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回通用应答：不支持
        reply(sim, 0x8303, sn, 2);
        return false;
    }

    logi("[0x8303] type=[%d]", req.type);
    int ret = 0;

    if (req.type == 0) {
        ret = DbHelper::getInstance().delInfoMenu();
        LOG_IF(ret != 0, logw, "[0x8303] Failed to del info menu.");

    } else {
        ret = DbHelper::getInstance().addInfoMenu(req.info_items);
        LOG_IF(ret != 0, logw, "[0x8303] Failed to add info menu.");
    }

    reply(sim, 0x8303, sn, ((ret == 0) ? 0 : 1));
    return true;
}

// 信息服务
bool JttClient::proc8304(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8304 req;

    if (!req.decode(data)) {
        logw("[0x8304] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回通用应答：不支持
        reply(sim, 0x8304, sn, 2);
        return false;
    }

    logi("[0x8304] type=[%d]", req.type);
    int ret = DbHelper::getInstance().updateInfoMsg(req.type, req.msg);
    LOG_IF(ret != 0, logw, "[0x8304] Failed to update info msg, type=[%d]", req.type);
    reply(sim, 0x8304, sn, ret == 0 ? 0 : 1);
    return true;
}

// 设置电话本
bool JttClient::proc8401(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8401 req;

    if (!req.decode(data)) {
        logw("[0x8401] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回通用应答：不支持
        reply(sim, 0x8304, sn, 2);
        return false;
    }

    logi("[0x8401] mode=[%d]", req.mode);
    int ret = ServiceHelper::getInstance().saveContacts(req);
    reply(sim, 0x8401, sn, ret == 0 ? 0 : 1);
    return true;
}
// 车辆控制
bool JttClient::proc8500(const my::string& sim, my::ushort sn, const my::constr& data)
{
    Manager & m = Manager::getInstance();
    my::uchar ctl = 0;
    my::constr tmp = data;
    tmp >> my::ntoh >> ctl;
    if (ctl & 1) {
        logd("lock the door!");
        m.current->setStateDoor1(false);
        m.current->setStateDoorLocker(true);
    } else {
        logd("open the door!");
        m.current->setStateDoor1(true);
        m.current->setStateDoorLocker(false);
    }
    X0500 resp;
    resp.sn = sn;
    Current st = ServiceHelper::getInstance().getStatus();
    resp.loc.lbi = ServiceHelper::getInstance().getLocationBaseInfo(st);
    JttMsg msg = resp.encode(sim, mProtVersion);
    return send(msg);
}

// 设置圆形区域
bool JttClient::proc8600(const my::string& sim, my::ushort sn, const my::constr& data)
{
    Manager & m = Manager::getInstance();
    X8600 req;

    if (!req.decode(data, mProtVersion)) {
        logw("[0x8600] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回通用应答：不支持
        reply(sim, 0x8600, sn, 2);
        return false;
    }
    m.mpLogger->mlog("%s > data = %s\n", __FUNCTION__, my::hex(data).c_str());

    logi("[0x8600] set circle area, type=[%d], toal=[%d]", req.type, req.total);
    bool success = true;

    AreaHelper & ah = AreaHelper::getInstance();
    if (req.type == 1) { // 追加区域
        for (auto it = req.circles.begin(); it != req.circles.end(); ++it) {
            bool suc = ah.addArea(it->toAreaPtr()) == 0;
            LOG_IF(!suc, loge, "[0x8600] Failed to add circle, id=[%d]", it->id);
            success = success ? suc : success;
        }

    } else if (req.type == 0 || req.type == 2) { // 更新区域、修改区域
        for (auto it = req.circles.begin(); it != req.circles.end(); ++it) {
            bool suc = ah.updateArea(it->toAreaPtr()) == 0;
            LOG_IF(!suc, loge, "[0x8600] Failed to update circle, id=[%d]", it->id);
            success = success ? suc : success;
        }
    }

    reply(sim, 0x8600, sn, success ? 0 : 1);


    extProcArea("电子围栏有更新");

    return true;
}

// 删除圆形区域
bool JttClient::proc8601(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8601 req;

    if (!req.decode(data)) {
        logw("[0x8601] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回通用应答：不支持
        reply(sim, 0x8601, sn, 2);
        return false;
    }

    logi("[0x8601] delete circle area, total=[%d]", req.total);

    if (req.total == 0) {
        LOG_IF(AreaHelper::getInstance().delArea(Area::Circle, 0) != 0, logw, "[0x8601] Failed to delete all circle");

    } else {
        for (auto it = req.ids.begin(); it != req.ids.end(); ++it) {
            LOG_IF(AreaHelper::getInstance().delArea(Area::Circle, *it) != 0, logw, "[0x8601] Failed to delete circle, id=[%d]", *it);
        }
    }

    reply(sim, 0x8601, sn, 0);

    extProcArea("电子围栏有更新");

    return true;
}

// 设置矩形区域
bool JttClient::proc8602(const my::string& sim, my::ushort sn, const my::constr& data)
{
    Manager & m = Manager::getInstance();
    X8602 req;

    if (!req.decode(data, mProtVersion)) {
        logw("[0x8602] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回通用应答：不支持
        reply(sim, 0x8602, sn, 2);
        return false;
    }
    m.mpLogger->mlog("%s > data = %s\n", __FUNCTION__, my::hex(data).c_str());

    logi("[0x8602] set rectange area, type=[%d], total=[%d], mProtVersion %d", req.type, req.total, mProtVersion);
    bool success = true;
    AreaHelper & ah = AreaHelper::getInstance();
    if (req.type == 1) { // 追加区域
        for (auto it = req.rects.begin(); it != req.rects.end(); ++it) {
            bool tmp = ah.addArea(it->toAreaPtr()) == 0;
            LOG_IF(!tmp, loge, "[0x8602] Failed to add rectange, id=[%d]", it->id);
            success = success ? tmp : success;
        }

    } else if (req.type == 0 || req.type == 2) { // 更新区域、修改区域
        for (auto it = req.rects.begin(); it != req.rects.end(); ++it) {
            bool tmp = ah.updateArea(it->toAreaPtr()) == 0;
            LOG_IF(!tmp, loge, "[0x8602] Failed to update rectange, id=[%d]", it->id);
            success = success ? tmp : success;
        }
    }

    reply(sim, 0x8602, sn, success ? 0 : 1);

    extProcArea("电子围栏有更新");

    return true;
}

// 删除矩形区域
bool JttClient::proc8603(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8603 req;

    if (!req.decode(data)) {
        logw("[0x8603] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回通用应答：不支持
        reply(sim, 0x8603, sn, 2);
        return false;
    }

    logi("[0x8603] delete rectange area, total=[%d]", req.total);

    if (req.total == 0) {
        LOG_IF(AreaHelper::getInstance().delArea(Area::Rectange, 0) != 0, logw, "[0x8603] Failed to delete all rectange.");

    } else {
        for (auto it = req.ids.begin(); it != req.ids.end(); ++it) {
            LOG_IF(AreaHelper::getInstance().delArea(Area::Rectange, *it) != 0, logw, "[0x8603] Failed to delete rectange, id=[%d]", *it);
        }
    }

    reply(sim, 0x8603, sn, 0);

    extProcArea("电子围栏有更新");

    return true;
}

// 设置多边形区域
bool JttClient::proc8604(const my::string& sim, my::ushort sn, const my::constr& data)
{
    Manager & m = Manager::getInstance();
    X8604 req;

    if (!req.decode(data, mProtVersion)) {
        logw("[0x8604] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回通用应答：不支持
        reply(sim, 0x8604, sn, 2);
        return false;
    }
    m.mpLogger->mlog("%s > data = %s\n", __FUNCTION__, my::hex(data).c_str());

    logi("[0x8604] set polygon area, id=[%d]", req.id);
    AreaHelper & ah = AreaHelper::getInstance();
    int r = ah.replaceArea(req.toAreaPtr());
    reply(sim, 0x8604, sn, r == 0 ? 0 : 1);

    extProcArea("电子围栏有更新");

    return true;
}

// 删除多边形区域
bool JttClient::proc8605(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8605 req;

    if (!req.decode(data)) {
        logw("[0x8605] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回通用应答：不支持
        reply(sim, 0x8605, sn, 2);
        return false;
    }

    logi("[0x8605] delete polygon, total=[%d]", req.total);

    if (req.total == 0) {
        LOG_IF(AreaHelper::getInstance().delArea(Area::Ploygon, 0) != 0, logw, "[0x8605] Failed to delete all polygon");

    } else {
        for (auto it = req.ids.begin(); it != req.ids.end(); ++it) {
            LOG_IF(AreaHelper::getInstance().delArea(Area::Ploygon, *it) != 0, logw, "[0x8605] Failed to delete polygon, id=[%d]", *it);
        }
    }

    reply(sim, 0x8605, sn, 0);

    extProcArea("电子围栏有更新");

    return true;
}

// 设置路线
bool JttClient::proc8606(const my::string& sim, my::ushort sn, const my::constr& data)
{
    Manager & m = Manager::getInstance();
    X8606 req;

    if (!req.decode(data, mProtVersion)) {
        logw("[0x8606] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回通用应答：不支持
        reply(sim, 0x8606, sn, 2);
        return false;
    }
    m.mpLogger->mlog("%s > data = %s\n", __FUNCTION__, my::hex(data).c_str());

    logi("[0x8606] set line area, id=[%d]", req.id);;
    AreaHelper & ah = AreaHelper::getInstance();
    int r = ah.replaceArea(req.toAreaPtr());
    reply(sim, 0x8606, sn, r == 0 ? 0 : 1);

    extProcArea("路线有更新");

    return true;
}

// 删除路线
bool JttClient::proc8607(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8607 req;

    if (!req.decode(data)) {
        logw("[0x8607] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回通用应答：不支持
        reply(sim, 0x8607, sn, 2);
        return false;
    }

    logi("[0x8607] delete line area, total=[%d]", req.total);

    if (req.total == 0) {
        LOG_IF(AreaHelper::getInstance().delArea(Area::LineArea, 0) != 0, logw, "[0x8607] Failed to delete all line area.");

    } else {
        for (auto it = req.ids.begin(); it != req.ids.end(); ++it) {
            LOG_IF(AreaHelper::getInstance().delArea(Area::LineArea, *it) != 0, logw, "[0x8607] Failed to delete line area, id=[%d]", *it);
        }
    }

    reply(sim, 0x8607, sn, 0);

    extProcArea("路线有更新");

    return true;
}
bool JttClient::proc8608(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8608 req;
    if (!req.decode(data)) {
        logw("Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回通用应答：不支持
        reply(sim, 0x8608, sn, 2);
        return false;
    }
    AreaHelper & ah = AreaHelper::getInstance();
    std :: vector < Area :: Ptr > areas;
    int ret = ah.getAreaList(areas, req.type);
    LOG_RETURN_IF(ret != 0, -1, loge, "failed to get areas.");
    logd("areas size = %d", areas.size());
    for (auto i : areas) {
        if (!req.areaIdNum || (req.idTbl.find(i->id) != req.idTbl.end())) {
            req.idTbl[i->id] = i;
            logd("area id %d", i->id);
        }
    }
    for (auto it = req.idTbl.begin(); it != req.idTbl.end();) {
        if (!it->second.get()) {
            it = req.idTbl.erase(it);
        } else {
            it++;
        }
    }
    X0608 resp;
    resp.type = req.type;
    JttMsg msg = resp.encode(sim, req.idTbl, mProtVersion);

    if (!send(msg)) {
        logw("%s > Failed to send X0608 response.", __FUNCTION__);
    }
    return true;
}

bool JttClient::proc8701(const my::string& sim, my::ushort sn, const my::constr& data)
{
    Manager & m = Manager::getInstance();
    X8701 req;

    if (!req.decode(data)) {
        logw("%s > Failed to decode, msg={\n%s\n}.", __FUNCTION__, my::hex(data, true).c_str());
        // 回复通用应答: 消息有误
        reply(sim, 0x8701, sn, 2);
        return false;
    }
    Config & config = m.config;
    switch (req.cmd)
    {
        case 0xc3: {
            try {
                json j;
                ifstream jf("/sdcard/run/can_input.json");
                jf >> j;
                j["m4_analog"]["aspeed"]["ratio"] = req.u.c3.pulseCoef;
                ofstream of("/sdcard/run/can_input.json");
                of << j.dump(4);
                system("stop hostio;start hostio");
            } catch (json::exception e) {
                loge("%s\n", e.what());
            }
            break;
        }
        case 0xc4: {
            logd("set systime %02x-%02x-%02x %02x:%02x:%02x",
                    req.u.c4.timeBcd[0], req.u.c4.timeBcd[1], req.u.c4.timeBcd[2],
                    req.u.c4.timeBcd[3], req.u.c4.timeBcd[4], req.u.c4.timeBcd[5]);
            logd("set setup time %02x-%02x-%02x %02x:%02x:%02x",
                    req.u.c4.setupTmBcd[0], req.u.c4.setupTmBcd[1], req.u.c4.setupTmBcd[2],
                    req.u.c4.setupTmBcd[3], req.u.c4.setupTmBcd[4], req.u.c4.setupTmBcd[5]);
#ifdef CHOUJIAN//通天星下发协议有问题，暂默认关闭校时
            struct timeval stime;
            stime.tv_sec = req.u.c4.realDt;
            settimeofday(&stime, NULL);
            // 设置硬件时钟
            system("qcom_date -R &");
#endif

            char req_resp[64] = {0};
            char req_cmd[128] = {0};
            sprintf(req_cmd, "cmd saveconf base init.date %d", req.u.c4.setupDt);
            int req_stat = LogCallProxyCmd::sendReq("config", req_cmd, req_resp, sizeof(req_resp), 1);
            LOG_RETURN_IF(req_stat != true, -1, loge, "failed to save [base] init.date, value=[%d]", req.u.c4.setupDt);
            config.sys.setup.date = req.u.c4.setupDt;

            sprintf(req_cmd, "cmd saveconf base init.mileage %f", req.u.c4.initMileAge * 10);
            req_stat = LogCallProxyCmd::sendReq("config", req_cmd, req_resp, sizeof(req_resp), 1);
            LOG_RETURN_IF(req_stat != true, -1, loge, "failed to save [base] init.mileage, value=[%f]", req.u.c4.initMileAge * 10);
            config.sys.setup.mileage_x10 = req.u.c4.initMileAge * 10;

            //todo //req.u.c4.totalMileAge;
            break;
        }
        default: {
            loge("invalid cmd 0x%x", req.cmd);
            return false;
        }
    }
    X0700 resp;
    resp.seq = sn;
    resp.cmd = req.cmd;
    JttMsg msg = resp.encode(sim, mProtVersion);

    if (!send(msg)) {
        logw("%s > Failed to send X0700 response.", __FUNCTION__);
    }
    return true;
}


// 上报驾驶员身份信息请求
bool JttClient::proc8702(const my::string& sim, my::ushort sn, const my::constr& data)
{
    Current st = ServiceHelper::getInstance().getStatus();
    X0702 res;
    res.state = st.ic.state;
    numstr2bcd(res.time, my::timestamp::YYMMDD_HHMMSS_S(st.ic.time), sizeof(res.time) * 2);
    res.ic_status = st.ic.ic_status;
    char name[128] = {0};
    my::utf8ToGbk((char*)st.ic.name.c_str(), name);
    res.name = name;
    char qc_code[128] = {0};
    my::utf8ToGbk((char*)st.ic.qc_code.c_str(), qc_code);

    res.qc_code = qc_code;
    char ca[128] = {0};
    my::utf8ToGbk((char*)st.ic.ca.c_str(), ca);
    res.ca = ca;
    date2bcd(st.ic.validity_date, res.validity_date);

    JttMsg msg = res.encode(sim, mProtVersion);
    if (!send(msg)) {
        loge("[0x8702] failed to send ic driver info to server.");
        return false;
    }

    return true;
}

// 多媒体数据上传应答
bool JttClient::proc8800(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8800 req;

    if (!req.decode(data)) {
        logw("[0x8800] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        return false;
    }

    logi("[0x8800] id=[%d], retry_num=[%d]", req.id, req.retry_num);
    return true;
}
bool JttClient::sendMedia(const my::string& sim, my::uint64 timeMs, std::string fileName, int ch)
{
    bool ret = true;
    char * jpg = (char *)malloc(1 << 20);
    if (jpg) {
        // 图片
        Media m;
        do {
            int jpgSize = 0;
            struct stat st;

            if (!stat(fileName.c_str(), &st)) {
                jpgSize = (int)st.st_size;
            }

            if (jpgSize <= 0) {
                ret = false;
                break;
            }

            FILE * fp = fopen(fileName.c_str(), "r");

            if (fp) {
                int rdsize = fread(jpg, 1, jpgSize, fp);

                if (jpgSize != rdsize) {
                    loge("file %s read fail!%d, total %d\n", fileName.c_str(), rdsize, jpgSize);
                }

                fclose(fp);
                unlink(fileName.c_str());
            }

            // 获取图片编号
            my::uint64 img_time = timeMs;
            // 获取当前位置
            m.loc = ServiceHelper::getInstance().getCurrentLocation(ServiceHelper::getInstance().getStatus());
            // 通道
            m.ch = ch;
            // 时间
            m.time = img_time / 1000;
            // 图片数据
            my::string img_data;
            img_data.assign((char*)jpg, (int)jpgSize);
            m.data = img_data;
        }while(0);
        free(jpg);

        JttMsg msg = X0801::encode(sim, timeMs, 0, 0, 0, ch, LocationBaseInfo::create(m.loc), m.data, mProtVersion);

        if (!send(msg)) {
            loge(" Failed to upload the jpg. %s\n", strerror(errno));
            ret = false;
        }
        else {
            logd(" Upload a jpg successfully, id=[%d]", (my::uint)timeMs);
        }
    }
    return ret;
}
void JttClient::autoSnap()
{
    Manager & m = Manager::getInstance();
    FIX_X_SNAP_PARAM fixTmSnap = m.config.sys.mFixTmSnapParam;
    FIX_X_SNAP_PARAM fixDistSnap = m.config.sys.mFixDistSnapParam;
#if 0//test
    fixTmSnap.time.enChBits = 1;
    fixTmSnap.time.isMin = 1;
    fixTmSnap.time.time = 1;
#endif
    if (fixTmSnap.i) {
        for (int i = 0; i < (m.config.sys.cameras + m.config.sys.ipcs); i++) {
            if (fixTmSnap.time.enChBits & (1 << i)) {
                int tm_secs = fixTmSnap.time.time;
                if (fixTmSnap.time.isMin) {
                    tm_secs *= 60;
                }
                if (tm_secs < 5) {
                    tm_secs = 5;
                }
                if (mFixTmLastSnap[i].elapsed() >= (tm_secs * 1000)) {
                    mFixTmLastSnap[i] = my::timestamp::now();
                    mkdir("/mnt/obb/.snap/", 0644);
                    std::string fileName = "/mnt/obb/.snap/snap_";
                    fileName += tag + "_";
                    fileName += std::to_string(time(NULL)) + "_ch";
                    fileName += std::to_string((int)i + 1) + "_fixTime.jpg";
                    std::string snapcmd = "cmd lms ";
                    snapcmd += std::to_string((int)i + 1) + " ";
                    snapcmd += fileName + " ";
                    snapcmd += std::to_string(704) + " ";
                    snapcmd += std::to_string(576) + " ";
                    snapcmd += "1";
                    logd("auto snap %s!\n", snapcmd.c_str());

                    vector<char> resp;
                    LogCallProxyCmd::sendReq("media", snapcmd.c_str(), resp);
                    mFixTmSnapFileList[i][(my::uint64)my::timestamp::now()] = fileName;
                } else {
                   // logd("ch%d snap interval:%d!\n", i+1, tm_secs);
                }
            }
        }
    }

    for (int i = 0; i < (m.config.sys.cameras + m.config.sys.ipcs); i++) {
        if (mFixTmSnapFileList[i].size()) {
            //send
            auto it = mFixTmSnapFileList[i].begin();
            while (it != mFixTmSnapFileList[i].end()) {
                if (!access(it->second.c_str(), R_OK)) {
                    if (((it->first + 300) <= (my::uint64)my::timestamp::now())) {
                        JttMsg msg = X0800::encode(sim, it->first, 0, 0, 0, i + 1, mProtVersion);
                        if (!send(msg)) {
                            loge("Failed to upload x0800. %s\n", strerror(errno));
                        }
                        else {
                            logd("Upload a jpg successfully, id=[%d]", it->first);
                        }
                        sendMedia(sim, it->first, it->second, i + 1);
                        it = mFixTmSnapFileList[i].erase(it);
                        break;
                    }
                    else {
                        //logd("%s = %d", it->second.c_str(), (my::uint64)my::timestamp::now() - it->first);
                    }
                } else {
                    loge("%d : %s not exist!", it->first, it->second.c_str());
                    if (((it->first + 3000) <= (my::uint64)my::timestamp::now())) {
                        m.mpLogger->mlog("snap fail! : %s!", it->second.c_str());
                        it = mFixTmSnapFileList[i].erase(it);
                        continue;
                    }
                }
                it++;
            }
        }
    }

#if 0
    if (fixDistSnap.i) {
        for (int i = 0; i < 8; i++) {
            if (fixTmSnap.dist.enChBits & (1 << i)) {
                //to do
            }
        }
    }
#endif

}

void JttClient::doSnapCmd()
{
    Manager & m = Manager::getInstance();
    ServiceHelper & sh = ServiceHelper::getInstance();
    my::uint64 curTm = my::timestamp::milliseconds_from_19700101();
    if (mSnapCmd.cmd != 0) { // 拍照
        uint16_t width = Resolution::getWidth(mSnapCmd.resolution), height = Resolution::getHeight(mSnapCmd.resolution);
        if (!mSnapCmd.lastSnapTm || (curTm > (mSnapCmd.lastSnapTm + 1000 * (my::uint64)mSnapCmd.inteval))) {
            if(mSnapCmd.lastSnapTm) {
                if (mSnapCmd.cmd != 0xFFFF) {
                    mSnapCmd.cmd--;
                } else {
                    mSnapCmd.cmd = 0;
                }
                if (!mSnapCmd.cmd) {
                    return;
                }
            }

            mSnapCmd.lastSnapTm = mSnapCmd.lastSnapTm ? curTm : mSnapCmd.curSnapTm;
            std::string snapFileName;
            snapFileName = "/mnt/obb/.snap/snap_";
            snapFileName += std::to_string(mSnapCmd.lastSnapTm) + "_ch";
            snapFileName += std::to_string((int)mSnapCmd.ch) + "_";

            std::string snapcmd;
            mkdir("/mnt/obb/.snap/", 0644);
            if (mSnapCmd.cmd != 0xffff) {
                snapFileName += std::to_string(mSnapCmd.cmd) + ".jpg";
                snapcmd = "cmd lms ";
                snapcmd += std::to_string((int)mSnapCmd.ch) + " ";
                snapcmd += snapFileName + " ";
                snapcmd += std::to_string((int)width) + " ";
                snapcmd += std::to_string((int)height) + " ";
                snapcmd += "1";
            } else {
                snapFileName += std::to_string(mSnapCmd.cmd);
                my::file::mkdir(snapFileName.c_str());
                snapcmd = "cmd snapshot 0 ";
                snapcmd += std::to_string((int)time(NULL)) + " ";
                snapcmd += std::to_string((int)mSnapCmd.ch) + " ";
                snapcmd += snapFileName + " ";
                snapcmd += std::to_string((int)time(NULL) - 1) + " ";
                snapcmd += std::to_string((int)time(NULL) + mSnapCmd.inteval);
                snapcmd += " 0 ";
                snapcmd += std::to_string(width) + " ";
                snapcmd += std::to_string(height);
            }
            logd("%s", snapcmd.c_str());

            vector<char> resp;
            LogCallProxyCmd::sendReq("media", snapcmd.c_str(), resp);
            if (mSnapCmd.cmd == 0xFFFF) {
                snapFileName += "/";
                snapFileName += std::to_string((int)mSnapCmd.ch) + ".mp4";
            }
            mSnap2send[snapFileName] = mSnapCmd;
        }
    }
    if (mSnap2send.size())
    {
        auto it = mSnap2send.begin();
        while (it != mSnap2send.end()) {
            int waitMs = 1000 + (it->second.cmd == 0xffff) * it->second.inteval * 1000 * 3;
            if ((it->second.lastSnapTm + waitMs) <= curTm) {
                break;
            }
            logd("wait for %s ...", it->first.c_str());
            it++;
        }
        if (it == mSnap2send.end()) {
            logd("none snap file ready!");
            return;
        }
        uint16_t width = Resolution::getWidth(it->second.resolution), height = Resolution::getHeight(it->second.resolution);
        FILE * fp = fopen(it->first.c_str(), "r");

        if (fp) {
            int jpgSize = 0;
            struct stat st;

            if (!stat(it->first.c_str(), &st)) {
                jpgSize = (int)st.st_size;
            }

            if (jpgSize <= 0) {
                // 拍照失败
                loge("[0x8801] Failed to take snapshot, ch=[%d], width=[%d], height=[%d]", it->second.ch, width, height);
                // 回复通用应答: 失败
                reply(sim, 0x8801, it->second.reqSn, 1);
                mSnap2send.erase(it);
            } else {
                logd("jpg %s size %d", it->first.c_str(), jpgSize);

                char * jpg = (char *)malloc(jpgSize);
                if (!jpg) {
                    loge("ch %d ringbuf error!\n", it->second.ch - 1);
                    // 回复通用应答: 失败
                    reply(sim, 0x8801, it->second.reqSn, 1);
                    fclose(fp);
                    return;
                }
                int rdsize = fread(jpg, 1, jpgSize, fp);

                if (jpgSize != rdsize) {
                    loge("file %s read fail!%d, total %d\n", it->first.c_str(), rdsize, jpgSize);
                }

                fclose(fp);

                // 图片
                Media m;
                m.media = (!!strstr(it->first.c_str(), ".mp4")) ? 2 : 0;
                m.event = 0;/*平台下发*/
                m.id = it->second.reqSn;
                // 获取图片编号
                my::uint64 img_time = it->second.lastSnapTm;
                // 获取当前位置
                m.loc = sh.getCurrentLocation(sh.getStatus());
                // 通道
                m.ch = it->second.ch;
                // 时间
                m.time = img_time / 1000;
                // 图片数据
                my::string img_data;
                img_data.assign((char*)jpg, (int)jpgSize);
                free(jpg);

                unlink(it->first.c_str());
                if (it->second.save) {
                    // 路径
                    std::string path = "/mnt/media_rw/sdcard1/MNEYE/media/";
                    my::file::mkdir(path.c_str());
                    path += strrchr(it->first.c_str(), '/');
                    m.path = path.c_str();
                    //m.path.assignf("%s/%d-%lld.jpg", m.getImagePath().c_str(), req.ch, img_time);

                    if (0 != sh.saveMedia(m, img_data)) {
                        loge("[0x8801] Failed to save data, ch=[%d], width=[%d], height=[%d]", it->second.ch, width, height);
                        // 回复通用应答: 失败
                        reply(sim, 0x8801, it->second.reqSn, 1);
                        return;
                    }
                } else { // 实时上传
                    m.data = img_data;
                    // ID列表
                    std::vector<Media> list;
                    list.push_back(m);
                    #if 0
                    JttMsg msg = X0805::encode(sim, it->second.reqSn, 0, list, mProtVersion);
                    if (!send(msg)) {
                        // 发送应答失败
                        loge("[0x8801] Failed to send X0805 response.");
                        mSnap2send.erase(it);
                        return;
                    }
                    #endif
                    for (int i = 0; i < (int)list.size(); ++i) {
                        // 上传多媒体文件
                        Media& m = list[i];
                        JttMsg msg = X0801::encode(sim, m.id, m.media, m.media ? 4 : 0, m.event, it->second.ch, LocationBaseInfo::create(m.loc), m.data, mProtVersion);

                        if (!send(msg)) {
                            loge("[0x8801] Failed to upload the jpg. %s\n", strerror(errno));
                            mSnap2send.erase(it);
                            return;
                        }

                        logi("[0x8801] Upload a jpg successfully, id=[%d]", m.id);
                    }
                }
                mSnap2send.erase(it);
            }
        } else {
            mSnap2send.erase(it);
        }
    }

}
// 抓取图片
bool JttClient::proc8801(const my::string& sim, my::ushort sn, const my::constr& data)
{
    Manager & m = Manager::getInstance();
    X8801 req;

    if (!req.decode(data)) {
        logw("[0x8801] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回复通用应答: 消息有误
        reply(sim, 0x8801, sn, 2);
        return false;
    }

    logd("[0x8801] ch=[%d], cmd=[%d], inteval=[%d], save=[%d], resolution=[%d], quality=[%d], brightness=[%d], contrast=[%d], saturability=[%d], chroma=[%d].",
         req.ch, req.cmd, req.inteval, req.save, req.resolution, req.quality,
         req.brightness, req.contrast, req.saturability, req.chroma);

    if ((m.config.sys.cameras + m.config.sys.ipcs) < req.ch) {
        logw("[0x8801] Channel '%d' not existed.", req.ch);

        // 回复通用应答: 不支持
        reply(sim, 0x8801, sn, 3);
        return false;
    }

    if (req.cmd != 0) { // 拍照
        mSnapCmd = req;
        mSnapCmd.lastSnapTm = 0;
        mSnapCmd.curSnapTm = my::timestamp::milliseconds_from_19700101();
        mSnapCmd.reqSn = sn;

#if 0
        reply(sim, 0x8801, sn, 0);/*不回应通用应答，避免过检平台判断失败*/
#else
        // 图片
        Media m;
        m.media = ((mSnapCmd.cmd == 0xffff) * 2);
        m.event = 0;/*平台下发*/
        m.id = mSnapCmd.reqSn;
        m.loc = ServiceHelper::getInstance().getCurrentLocation(ServiceHelper::getInstance().getStatus());
        // 通道
        m.ch = mSnapCmd.ch;
        // 时间
        m.time = mSnapCmd.curSnapTm / 1000;

        // ID列表
        std::vector<Media> list;
        list.push_back(m);
        JttMsg msg = X0805::encode(sim, mSnapCmd.reqSn, 0, list, mProtVersion);
        if (!send(msg)) {
            // 发送应答失败
            loge("[0x8801] Failed to send X0805 response.");
        }
#endif
    } else { // 不支持
        reply(sim, 0x8801, sn, 3);
    }
    return true;
}

// 存储多媒体数据检索
bool JttClient::proc8802(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8802 req;

    if (!req.decode(data)) {
        logw("[0x8802] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回复通用应答: 消息有误
        reply(sim, 0x8802, sn, 2);
        return false;
    }

    logi("[0x8802] query multi-media data, meida=[%d], ch=[%d], event=[%d]", req.media, req.ch, req.event);

    if (req.media == 0 || req.media == 2) {
        std::vector<Media> list;
        DbHelper & dbh = DbHelper::getInstance();
        int ret = dbh.getMediaList(list, req);

        if (0 != ret) {
            reply(sim, 0x8802, sn, 2);
            return false;
        }

        X0802 rsp;
        rsp.sn = sn;

        for (int i = 0; i < list.size(); ++i) {
            X0802::MediaItem item;
            Media& m = list[i];
            item.id = m.id;
            item.ch = m.ch;
            item.media = m.media;
            item.event = m.event;
            item.location.lbi = LocationBaseInfo::create(m.loc);
            rsp.items.push_back(item);
        }

        JttMsg msg = rsp.encode(sim, mProtVersion);

        if (!send(msg)) {
            logw("[0x8802] Failed to send X0802 response.");
            return false;
        }

        logi("[0x8802] response a media query successfully.");
    } else {
        reply(sim, 0x8802, sn, 3);
    }

    return true;
}

// 存储多媒体数据上传命令
bool JttClient::proc8803(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8803 req;

    if (!req.decode(data)) {
        logw("[0x8803] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回复通用应答: 消息有误
        reply(sim, 0x8803, sn, 2);
        return false;
    }

    logi("[0x8803] query upload data, meida=[%d], ch=[%d], event=[%d]", req.x8802.media, req.x8802.ch, req.x8802.event);
    std::vector<Media> list;
    bool res = false;

    do {
        int ret = DbHelper::getInstance().getMediaList(list, req.x8802);

        if (ret != 0) {
            loge("[0x8803] Failed to get media from db.");
            break;
        }

        auto it = list.begin();

        for (; it != list.end(); ++it) {
            Media& m = *it;
            my::file f;
            my::int64 size = f.open(m.path.c_str(), "rb");

            if (size <= 0) {
                loge("[0x8803] Failed to open media file, path=[%s]", m.path.c_str());
                break;
            }
            m.data.appendSize(size);
            size = f.gets((char*)m.data, m.data.length());

            if (size != m.data.length()) {
                loge("[0x8803] Failed to read media file, path=[%s]", m.path.c_str());
                f.close();
                break;
            }

            f.close();

            JttMsg msg = X0801::encode(sim, m.id, 0, 0, 0, m.ch, LocationBaseInfo::create(m.loc), m.data, mProtVersion);

            if (!send(msg)) {
                loge("[0x8803] Failed to upload the jpg.");
                break;
            }

            if (req.flag == 1) { // 删除
                if (DbHelper::getInstance().delMedia(m.id) != 0 || my::file::rm(m.path.c_str()) != 0) {
                    loge("[0x8803] Failed to remove media, id=[%d]", m.id);
                    break;
                }
            }
        }

        res = (it == list.end());
    } while (0);

    reply(sim, 0x8803, sn, res ? 0 : 1);
    return true;
}
// 录音命令
bool JttClient::proc8804(const my::string& sim, my::ushort sn, const my::constr& data)
{
    if (data.length() < 5) {
        reply(sim, 0x8804, sn, 2);
        return false;
    }
    my::uchar recAud;
    my::ushort recSec;
    my::uchar saveFlag, sampleRate;
    my::constr msgData = data;

    msgData >> my::ntoh >> recAud >> recSec >> saveFlag >> sampleRate;
    if (recAud) {//开始录音
        // todo
    } else {
        // todo
    }
    reply(sim, 0x8804, sn, 0);
    return true;
}

// 单条存储多媒体数据检索上传命令
bool JttClient::proc8805(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8805 req;

    if (!req.decode(data)) {
        logw("[0x8805] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回复通用应答: 消息有误
        reply(sim, 0x8805, sn, 2);
        return false;
    }

    logi("[0x8805] id=[%d] flag=[%d]", req.id, req.flag);

    std::vector<Media> list;
    X8802 cond;
    bool res = false;

    do {
        int ret = DbHelper::getInstance().getMediaList(list, cond, req.id);

        if (ret != 0 || list.size() != 1) {
            loge("[0x8805] Failed to get media from db, id=[%d]", req.id);
            break;
        }

        Media& m = list[0];
        my::file f;
        my::int64 size = f.open(m.path.c_str(), "rb");

        if (size <= 0) {
            loge("[0x8805] Failed to open media file, path=[%s]", m.path.c_str());
            break;
        }

        m.data.appendSize(size);
        size = f.gets((char*)m.data, m.data.length());

        if (size != m.data.length()) {
            loge("[0x8805] Failed to read media file, path=[%s]", m.path.c_str());
            f.close();
            break;
        }

        f.close();

        JttMsg msg = X0801::encode(sim, m.id, 0, 0, 0, m.ch, LocationBaseInfo::create(m.loc), m.data, mProtVersion);

        if (!send(msg)) {
            loge("[0x8805] Failed to upload the jpg.");
            return false;
        }

        if (req.flag == 1) { // 删除
            if (DbHelper::getInstance().delMedia(req.id) != 0 || my::file::rm(m.path.c_str()) != 0) {
                loge("[0x8805] Failed to remove media, id=[%d]", req.id);
                break;
            }
        }

        res = true;
    } while (0);

    reply(sim, 0x8805, sn,  res ? 0 : 1);
    return true;
}
bool JttClient::proc8900(const my::string& sim, my::ushort sn, const my::constr& data)
{
    Manager & m = Manager::getInstance();
    Config & cfg = m.config;
    X8900 req;
    if (!req.decode(data)) {
        loge("msg decode error!");
        return false;
    }
    int result = 0;
    switch (req.type) {
        case 0x00: { //GNSS模块详细定位数据
            //todo
            break;
        }
        case 0x41: { // 串口1透传信息
            break;
        }
        case 0x42: { // 串口2透传信息
            break;
        }
        case 0x0B: { //道路运输证IC卡信息
            //break;
        }
        default : {
            if (!proc8900_ext(sim, sn, req)) {
                //result = 1;
                m.mpLogger->mlog("%s > id %#x, data = %s\n", __FUNCTION__, req.type, my::hex(data).c_str());
                my::file f;
                char fileName[128];
                snprintf(fileName, sizeof(fileName), "/data/0x8900_0x%x.log", req.type);
                f.open(fileName);
                f.puts(req.msg.c_str(), req.msg.length());
                f.close();
            }
            //logd("data for ext : %s", (const char*)data);
        }
    }
    reply(sim, 0x8900, sn, result);
    return true;
}
bool JttClient::proc9003(const my::string& sim, my::ushort sn, const my::constr& data)
{
    Manager & m = Manager::getInstance();
    Config & cfg = m.config;
    X1003 rsp;

    rsp.audCodeTyp = 8;
    rsp.audSampleLen = 204;
    char propAudioValue[PROP_VALUE_MAX] = {0};
    memset(propAudioValue, 0, sizeof(propAudioValue));
    __system_property_get(PROP_PERSIST_MINIEYE_AUDIO_ENCODE, propAudioValue);
    if (0 == strcmp(propAudioValue, "g711a")) {
        rsp.audCodeTyp = 6;
    } else if (0 == strcmp(propAudioValue, "g711u")) {
        rsp.audCodeTyp = 7;
    } else if (0 == strcmp(propAudioValue, "g726")) {
        rsp.audCodeTyp = 8;
    } else if (0 == strcmp(propAudioValue, "aac")) {
        rsp.audCodeTyp = 19;
    } else {
        rsp.audCodeTyp = 8;
    }
    rsp.audChNum = 2;
    rsp.audSampleRate = 0;
    rsp.audSampleBit = 1;
    rsp.audOutEn = 1;
    rsp.videoCodeTyp = 98;
    rsp.audPhyNum = 2;
    rsp.videoPhyNum = cfg.sys.cameras;

    JttMsg msg = rsp.encode(sim, mProtVersion);
    if (!send(msg)) {
        loge(" Failed to send audio/video prop.");
        return false;
    }
    return true;
}

// 播放实时音视频
/*命令格式：rtp+*/
bool JttClient::proc9101(const my::string& sim, my::ushort sn, const my::constr& data)
{
    char pv[PROP_VALUE_MAX] = {0};
    __system_property_get(PROP_PERSIST_MINIEYE_TALK_CH_BASE, pv);
    int talkChBase = atoi(pv);
    Manager & m = Manager::getInstance();
    if (talkChBase < (m.config.sys.cameras + m.config.sys.ipcs)) {
        /* 客户定制，语音对讲监听未使用与视频通道一致的通道号，需增加偏移量进行转换 */
        talkChBase = 32;;
    }

    char audio[PROP_VALUE_MAX] = {0};
    memset(audio, 0, sizeof(audio));
    __system_property_get(PROP_PERSIST_JT808_LIVEWITHOUTAUDIO, audio);
    bool liveWithoutAudio = (atoi(audio) > 0);

    X9101 req;

    if (!req.decode(data)) {
        logw("[0x9101] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回复通用应答: 消息有误
        reply(sim, 0x9101, sn, 2);
        return false;
    }

    logi("[0x9101] ch=[%d], meida=[%d], quality=[%d], ip=[%s], port=[%d]", req.ch, req.media, req.quality, req.server.c_str(), req.tcp_port);
    IpcCmd ipc;
    bool v = false, a = false;

    struct hostent * pHost = gethostbyname((char*)req.server.c_str());
    if (!pHost) {
        loge(" gethostbyname %s failed %s", (char*)req.server.c_str(), hstrerror(h_errno));
        return false;
    }

    my::string ip = inet_ntoa(*((struct in_addr *)pHost->h_addr));

    logd("domain : %s, IP Address : %s\n", (char*)req.server.c_str(), ip.c_str());

    int32_t ch = getChnByCustomCh(req.ch);
    if (ch == -1) {
        ch = req.ch;
    }

    logd("ch:%d, mapCh:%d", req.ch, ch);

    if (req.media == 0 || req.media == 1) {
        v = true;
        ipc.reset().add("cmd").add("rtp+").add(ch).add(getMedia(req.quality).c_str()).add(this->tag)
                        .add(ip).add(req.tcp_port).add(sim).add((req.media || liveWithoutAudio) ? ch : -1);
        int ret = MediaHelper::getInstance().sendIpc(ipc);
        logd("[0x9101] play video, rspOk=[%s]", ipc.rspOkString().c_str());
    }

    if (req.media == 2) {
        a = true;
        ipc.reset().add("cmd").add("rtp+").add(ch % talkChBase).add("A").add(this->tag)
                        .add(ip).add(req.tcp_port).add(sim).add(ch);
        int ret = MediaHelper::getInstance().sendIpc(ipc);
        logd("[0x9101] paly audio, rspOk=[%s]", ipc.rspOkString().c_str());
    }

    if (req.media == 3) {
        a = true;
        ipc.reset().add("cmd").add("rtp+").add(ch % talkChBase).add("a").add(this->tag)
                        .add(ip).add(req.tcp_port).add(sim).add(ch);
        int ret = MediaHelper::getInstance().sendIpc(ipc);
        logd("[0x9101] paly audio, rspOk=[%s]", ipc.rspOkString().c_str());
    }

    reply(sim, 0x9101, sn, v || a ? 0 : 3);

    return true;
}

// 音视频实时传输控制
bool JttClient::proc9102(const my::string& sim, my::ushort sn, const my::constr& data)
{
    char pv[PROP_VALUE_MAX] = {0};
    __system_property_get(PROP_PERSIST_MINIEYE_TALK_CH_BASE, pv);
    int talkChBase = atoi(pv);
    Manager & m = Manager::getInstance();
    if (talkChBase < (m.config.sys.cameras + m.config.sys.ipcs)) {
        talkChBase = 32;
    }
    X9102 req;

    if (!req.decode(data)) {
        logw("[0x9102] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        reply(sim, 0x9102, sn, 2); // 消息有误
        return false;
    }

    logd("[0x9102] ch=[%d], cmd=[%d], option=[%d], code=[%d]", req.ch, req.cmd, req.option, req.code);

    IpcCmd ipc;

    int32_t ch = getChnByCustomCh(req.ch);
    if (ch == -1) {
        ch = req.ch;
    }

    logd("ch:%d, mapCh:%d", req.ch, ch);

    if (req.cmd == 0) { // 关闭音视频
        if (req.option == 0 || req.option == 2) {
            ipc.reset().add("cmd").add("rtp-").add(ch).add("v").add(this->tag);
            int ret = MediaHelper::getInstance().sendIpc(ipc);
            logd("[0x9102] close video v rspOk=[%s]", ipc.rspOkString().c_str());
            ipc.reset().add("cmd").add("rtp-").add(ch).add("V").add(this->tag);
            ret = MediaHelper::getInstance().sendIpc(ipc);
            logd("[0x9102] close video V rspOk=[%s]", ipc.rspOkString().c_str());
        }

        if (req.option == 0 || req.option == 1) {
            ipc.reset().add("cmd").add("rtp-").add(ch % talkChBase).add("a").add(this->tag);
            int ret = MediaHelper::getInstance().sendIpc(ipc);
            logd("[0x9102] close audio a rspOK=[%s]", ipc.rspOkString().c_str());
            ipc.reset().add("cmd").add("rtp-").add(ch % talkChBase).add("A").add(this->tag);
            ret = MediaHelper::getInstance().sendIpc(ipc);
            logd("[0x9102] close audio A rspOK=[%s]", ipc.rspOkString().c_str());
        }

        reply(sim, 0x9102, sn, 0);

    } else if (req.cmd == 1) { // 切换码流
        if (req.option == 0 || req.option == 1) { // 主码流
            ipc.add("cmd").add("watch").add("start").add(ch).add(req.option).add(this->tag);
            int ret = MediaHelper::getInstance().sendIpc(ipc);
            reply(sim, 0x9102, sn, ipc.rspOk() ? 0 : 1);

        } else {
            logw("[0x9102] Bad video stream code: %d.", req.option);
            reply(sim, 0x9102, sn, 3); // 其他参数不支持
            return false;
        }

    } else if (req.cmd == 4) { // 关闭语音对讲
        ipc.reset().add("cmd").add("rtp-").add(ch % talkChBase).add("A").add(this->tag);
        int ret = MediaHelper::getInstance().sendIpc(ipc);
        logd("[0x9102] close two-way rradio rspOK=[%s]", ipc.rspOkString().c_str());
        reply(sim, 0x9102, sn, ipc.rspOk() ? 0 : 3);

    } else {
        logw("[0x9102] Not suppored cmd: %d.", req.cmd);
        reply(sim, 0x9102, sn, 3);
        return false;
    }

    return true;
}

// 实时音视频传输状态通知
bool JttClient::proc9105(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X9105 req;

    if (!req.decode(data)) {
        logw("[0x9105] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回复通用应答: 消息有误
        reply(sim, 0x9105, sn, 2);
        return false;
    }

    logd("[0x9105] ch=[%d], loss=[%d]]", req.ch, req.loss);
    reply(sim, 0x9105, sn, 0);
    return true;
}

// 平台下发远程录像回放请求
bool JttClient::proc9201(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X9201 req;

    if (!req.decode(data)) {
        logw("[0x9201] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        // 回复通用应答: 消息有误
        reply(sim, 0x9201, sn, 2);
        return false;
    }

    struct hostent * pHost = gethostbyname((char*)req.server_ip.c_str());
    if (!pHost) {
        loge(" gethostbyname %s failed %s", (char*)req.server_ip.c_str(), hstrerror(h_errno));
        return false;
    }

    my::string ip = inet_ntoa(*((struct in_addr *)pHost->h_addr));
    logd("domain : %s, IP Address : %s\n", (char*)req.server_ip.c_str(), ip.c_str());
    req.server_ip = ip;

    int32_t ch = getChnByCustomCh(req.ch);
    if (ch == -1) {
        ch = req.ch;
    }

    X9205 cond;
    cond.ch = ch;
    memcpy(cond.s_time, req.s_time, sizeof(cond.s_time));
    memcpy(cond.e_time, req.e_time, sizeof(cond.e_time));
    cond.media = req.media;
    cond.code = req.code;
    cond.storage = req.storage;
    cond.alarm_tag = -1;

    my::string s_time, e_time;
    bcd2numstr(s_time, req.s_time, sizeof(req.s_time));
    bcd2numstr(e_time, req.e_time, sizeof(req.e_time));

    logi("[0x9201] ch=[%d], s_time=[%s], e_time[%s], media=[%d], code=[%d], storage=[%d]",
         ch, s_time.c_str(), e_time.c_str(),  req.media, req.code, req.storage);
    std::vector<NewMedia> list;
    my::uint begin = 0, end = 0;
    bcd2time(req.s_time, sizeof(req.s_time), begin);
    bcd2time(req.e_time, sizeof(req.e_time), end);
    int ret = recordQuery(ch, req.code, begin, end, list);
    if (ret <= 0) {
        logw("[0x9201] Failed to get media list from record, ret=[%d]", ret);
        // 回复通用应答: 消息处理失败
        reply(sim, 0x9201, sn, 1);
        return false;
    }

    X1205 rsp;
    rsp.sn = sn;

    for (int i = 0; i < list.size(); ++i) {
        rsp.list.push_back(X1205::NewMediaItem::create(list[i]));
    }

    JttMsg msg = rsp.encode(sim, mProtVersion);

    if (!send(msg)) {
        logw("[0x9201] Failed to send X1205 response.");
        return false;
    }

    //"start file playback: [ch] [strmIdx] [begin tick] [end tick] [ip] [port] [connection].\n"
    std::string pbCmd = "cmd rtpPb+ ";
    pbCmd += std::to_string((int)ch) + " ";
    pbCmd += std::to_string((int)((req.code == 2) ? 1 : 0)) + " ";
    pbCmd += std::to_string(begin) + " ";
    pbCmd += std::to_string(end) + " ";
    pbCmd += req.server_ip + " ";
    pbCmd += std::to_string(req.tcp_port) + " ";
    pbCmd += sim + " ";
    pbCmd += this->tag;
    pbCmd += " 0 0 ";/*标准1078，音视频，*/
    int mode = 0;
    switch(req.mode) {
        case 1: //快进
            mode = 3;
            break;
        case 2: //关键帧快退
            mode = 4;
            break;
        case 3: //关键帧
            mode = 6;
            break;
        case 4: //单帧
            mode = 6;
            break;
        default:
            break;
    }
    pbCmd += std::to_string(mode) + " ";
    pbCmd += std::to_string((int)req.speed);

    logd("%s", pbCmd.c_str());
    vector<char> resp;

    if (!LogCallProxyCmd::sendReq("recorder", pbCmd.c_str(), resp)) {
        loge("pb cmd fail! %s", pbCmd.c_str());
    }

    return true;
}

// 平台下发远程录像回放控制
bool JttClient::proc9202(const my::string& sim, my::ushort sn, const my::constr& data)
{
    int ret = 0;
    X9202 req;

    if (!req.decode(data)) {
        logw("[0x9202] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        // 回复通用应答: 消息有误
        reply(sim, 0x9202, sn, 2);
        return false;
    }

    int32_t ch = getChnByCustomCh(req.ch);
    if (ch == -1) {
        ch = req.ch;
    }

    std::string pbCmd = "";

    switch (req.ctl) {
        case 2: {
                pbCmd = "cmd rtpPb- ";
                pbCmd += std::to_string((int)ch) + " 0 ";
                pbCmd += this->tag;
                break;
            }

        case 5: {
                my::uint seek = 0;
                bcd2time((char*)req.time, sizeof(req.time), seek);
                pbCmd = "cmd rtpPbSeek ";
                pbCmd += std::to_string((int)ch) + " ";
                pbCmd += std::to_string((int)seek) + " ";
                pbCmd += this->tag;
                break;
            }

        case 0:
        case 1:
        case 3:
        case 4:
        case 6:
            pbCmd = "cmd rtpPbMode ";
            pbCmd += std::to_string((int)ch) + " ";
            pbCmd += std::to_string((int)req.ctl) + " ";
            pbCmd += std::to_string((int)req.speed) + " ";
            pbCmd += this->tag;
            break;
        default: {
                logd("%s > ctl %d not support!", __FUNCTION__, req.ctl);
                break;
            }
    }

    if (pbCmd != "") {
        logd("%s", pbCmd.c_str());
        vector<char> resp;

        if (!LogCallProxyCmd::sendReq("recorder", pbCmd.c_str(), resp)) {
            loge("pb cmd fail! %s", pbCmd.c_str());
            ret = 1;
        }

    } else {
        ret = 1;
    }

    reply(sim, 0x9202, sn, !!ret);
    return true;
}
int32_t JttClient::SOSDataQuery(int ch, int code, uint32_t begin, uint32_t end, std :: vector < NewMedia > & list)
{
    int total = 0;
    const char * sosPath = "/mnt/media_rw/sdcard1/MNEYE/SOS/";
    DIR * pd = opendir(sosPath);
    if (pd) {
        struct dirent *dp = NULL;
        struct stat st;
        while ((dp = readdir(pd)) != NULL) {
            if ('.' == dp->d_name[0]) {
                continue;
            }

            char p[256] = {0};
            snprintf(p, sizeof(p) - 1, "%s/%s/%d.mp4", sosPath, dp->d_name, ch);
            if (-1 == lstat(p, &st)) {
                continue;
            }

            if (!S_ISDIR(st.st_mode)) {
                int Y, M, D, h, m, s;
                int ret = sscanf(dp->d_name, "%04d%02d%02d%02d%02d%02d", &Y, &M, &D, &h, &m, &s);
                time_t         now = time(NULL);
                struct tm      tm_local;
                struct tm      tm_utc;
                unsigned long  time_local, time_utc;
                gmtime_r(&now, &tm_utc);
                localtime_r(&now, &tm_local);
                tm_local.tm_isdst = -1;
                tm_utc.tm_isdst = -1;

                #define TM_SET(_tm, _Y, _M, _D, _h, _m, _s) do {\
                    _tm.tm_year = _Y - 100; _tm.tm_mon = _M - 1; _tm.tm_mday = _D;\
                    _tm.tm_hour = _h; _tm.tm_min = _m; _tm.tm_sec = _s;\
                } while(0)

                TM_SET(tm_utc, Y, M, D, h, m, s);
                TM_SET(tm_local, Y, M, D, h, m, s);

                time_local = mktime(&tm_local);
                time_utc = mktime(&tm_utc);
                logd("time_utc %d, time_local %d", time_utc, time_local);

                uint32_t evtTm = time_utc;
                uint32_t dataBgn = evtTm - 5;
                uint32_t dataEnd = evtTm + 10;
                if (((!begin) && (!end)) ||
                    ((begin <= dataBgn) && (dataBgn < end)) ||
                    ((dataBgn <= begin) && (begin < dataEnd))) {
                    NewMedia nm;
                    nm.id = (int)evtTm;
                    nm.ch = ch;
                    nm.td = {dataBgn, dataEnd};
                    nm.alarm_tag = 0;
                    nm.meida = 2;
                    int strm = 1;//子流
                    nm.code = strm + 1;
                    nm.storage = 1;
                    nm.size = st.st_size;
                    nm.path = p;
                    list.push_back(nm);
                    total++;
                }
            } else {
                logd("skip dir %s\n", p);
            }
        }
        closedir(pd);
    }
    return total;
}

int32_t JttClient::recordQuery(int ch, int code, uint32_t begin, uint32_t end, std :: vector < NewMedia > & list,
                                        my::uint64 alarmTg, my::uchar mediaType)
{
    int32_t totalRecord = 0;
    int startIdx = 0;

    do {
        int once = 0;
        std::string querycmd = "cmd queryFrame ";
        querycmd += std::to_string(ch) + " ";                        // ch
        querycmd += std::to_string((int)(code == 2) ? 1 : 0) + " ";  // strm
        uint64_t bgn64 = begin;
        bgn64 *= 1000000;
        uint64_t end64 = end;
        end64 *= 1000000;
        querycmd += std::to_string(bgn64) + " ";
        querycmd += std::to_string(end64) + " ";
        querycmd += std::to_string(1000000000) + " ";
        uint64_t eventBits = (alarmTg == 0) ? ((uint64_t) -1) : alarmTg;
        querycmd += std::to_string(eventBits);

        logd("alarmTg:%" FMT_LLD "!%s", alarmTg, querycmd.c_str());
        vector<char> resp;
        LogCallProxyCmd::sendReqWithTimeOutEx("recorder", querycmd.c_str(), resp, 2000); // 至少5s

        if (resp.size() <= 0) {
            return false;
        }

        const char *data = (const char *) resp.data();

        data = strchr(data, '{');
        if (!data) {
            loge("invalid json!");
            return false;
        }

        logd("==> resp: %s", data);

        jsonUtil::jsonObject_t jsonObject = jsonUtil::stringToJsonObject(data);
        int32_t count = jsonUtil::getIntValue(jsonObject, "count");

        logd("ts file query count: %d", count);
        count = MIN(count, 100000);
        totalRecord = count;

        for (int32_t i = 0; i < count; i ++) {

            jsonUtil::jsonObject_t elemObj = jsonUtil::getJsonObjectValue(jsonObject, my::to_string(i).c_str());
            int bgnSec = jsonUtil::getUnsignedLongLongValue(elemObj, "bgnUsTimestamp") / 1000000, 
                endSec = jsonUtil::getUnsignedLongLongValue(elemObj, "endUsTimestamp") / 1000000;

            eventBits = jsonUtil::getUnsignedLongLongValue(elemObj, "evtBits");
            if (bgnSec == endSec) {
                logd("skip %d: %d ~ %d", i, bgnSec, endSec);
                continue;
            }

            int dataSize = jsonUtil::getIntValue(elemObj, "dataSize");
            NewMedia nm;
            nm.id = (int)jsonUtil::getIntValue(elemObj, "tsIndex");
            nm.ch = (int)jsonUtil::getIntValue(elemObj, "ch") + 1;
            nm.td = {bgnSec, endSec};
            nm.alarm_tag = eventBits;
            nm.meida = 0;
            nm.code = 1;
            nm.storage = 1;
            nm.size = dataSize;
            list.push_back(nm);
            once++;
        }

        jsonUtil::jsonObjectDestroy(jsonObject);

    } while (0);

    return totalRecord;
}
// 查询资源列表
bool JttClient::proc9205(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X9205 req;

    if (!req.decode(data)) {
        logw("[0x9205] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        // 回复通用应答: 消息有误
        reply(sim, 0x9205, sn, 2);
        return false;
    }

    int32_t ch = getChnByCustomCh(req.ch);
    if (ch == -1) {
        ch = req.ch;
    }

    my::string s_time, e_time;
    bcd2numstr(s_time, req.s_time, sizeof(req.s_time));
    bcd2numstr(e_time, req.e_time, sizeof(req.e_time));

    logi("[0x9205] ch=[%d], s_time=[%s], e_time[%s], alarm=[%ld], media=[%d], code=[%d], storage=[%d]",
         ch, s_time.c_str(), e_time.c_str(), req.alarm_tag, req.media, req.code, req.storage);

    std::vector<NewMedia> list;
    my::uint begin = 0, end = 0;
    bcd2time(req.s_time, sizeof(req.s_time), begin);
    bcd2time(req.e_time, sizeof(req.e_time), end);
    int ret = recordQuery(ch, req.code, begin, end, list, req.alarm_tag, req.media);
#if 0
    //int ret = MediaHelper::getInstance().searchVideo(list, req);
    if (!ret) {
        logw("[0x9205] Failed to get new media from db, ret=[%d]", ret);
        reply(sim, 0x9205, sn, 1);
        return false;
    }
#endif

    X1205 rsp;
    rsp.sn = sn;

    for (int i = 0; i < list.size(); ++i) {
        rsp.list.push_back(X1205::NewMediaItem::create(list[i]));
    }

    JttMsg msg = rsp.encode(sim, mProtVersion);

    if (!send(msg)) {
        logw("[0x9205] Failed to send X1205 response.");
        return false;
    }

    logi("[0x9205] send a media list successfully.");
    return true;
}
#define FAKE_8700
#ifdef FAKE_8700
#define BCDBYTE(_V) (((((_V) / 10) << 4) | (((_V) % 10) << 0)) & 0xff)
#define BCDTIME(_bcd, _Y, _M, _D, _h, _m, _s) do {\
    _bcd[0] = BCDBYTE(_Y);\
    _bcd[1] = BCDBYTE(_M);\
    _bcd[2] = BCDBYTE(_D);\
    _bcd[3] = BCDBYTE(_h);\
    _bcd[4] = BCDBYTE(_m);\
    _bcd[5] = BCDBYTE(_s);\
} while(0)
#define TIMEKEY(_key, _Y, _M, _D, _h, _m, _s) do {\
    _key[0] = BCDBYTE(_s);\
    _key[1] = BCDBYTE(_m);\
    _key[2] = BCDBYTE(_h);\
    _key[3] = BCDBYTE(_D);\
    _key[4] = BCDBYTE(_M);\
    _key[5] = BCDBYTE(_Y);\
} while(0)

struct MinusDataBlk
{
    my::uchar  timeBcd[6];//the byte for second : [5] = 0
    struct {
        my::uchar  spd;
        my::uchar  status;
    } __attribute__((packed))  secondData[60];
}__attribute__((packed));

struct X08 {
    std::map<uint64_t, MinusDataBlk, greater<uint64_t>> dataBlks;
    bool encode(std::vector<my::string> & data, int maxNum)
    {
        if (!dataBlks.size()) {
            return false;
        }
        int secNum = 0, minNum = 0;

        for (auto it : dataBlks) {
            if (secNum >= maxNum){
                break;
            }
            int idx = minNum / 7;
            if (data.size() < (idx + 1)) {
                my::string tmp;
                data.push_back(tmp);
                data[idx] << my::hton;
            }
            my::string & msg = data[idx];
            msg.append((const char *)it.second.timeBcd, sizeof(it.second.timeBcd));
            for (int i = 0; i < ARRAY_SIZE(it.second.secondData); i++) {
                msg << it.second.secondData[i].spd;
                msg << it.second.secondData[i].status;
                secNum++;
            }
            minNum++;
            logd("[%d] %012lx", secNum, it.first);
        }
        return true;
    }
};
bool X08FakeDataParse(const char * path, std::vector<my::string> & data, my::uchar bgn[6], my::uchar end[6], int n)
{
    FILE * fp = fopen(path, "r");
    if (!fp) {
        logw("open %s fail!", path);
        return false;
    }
    char line[1024];
    struct X08 param;
    int total = 0;
    while(fgets(line, sizeof(line), fp)) {
        uint32_t Y, M, D, h, m, s, ms, spd, stat;
        int ret = sscanf(line, "%d-%d-%d %d:%d:%d.%d,%d,%x",
            &Y, &M, &D, &h, &m, &s, &ms, &spd, &stat);
        if (9 == ret) {
            my::uchar timeBcd[6];
            BCDTIME(timeBcd, Y - 2000, M, D, h, m, 0);
            if (memcmp(timeBcd, bgn, 5) < 0 ||
                memcmp(timeBcd, end, 5) > 0) {
                //logd("bgn = %lx end = %lx, cur = %lx", *((uint64_t*)bgn), *((uint64_t*)end), *((uint64_t*)timeBcd));
                continue;
            }
            uint64_t key = 0;
            TIMEKEY(((my::uchar*)&key), Y - 2000, M, D, h, m, 0);
            auto it = param.dataBlks.find(key);
            logd("%d-%d-%d %d:%d:%d.%d,%d,%d, ret = %d",
                Y, M, D, h, m, s, ms, spd, stat, ret);
            if (it != param.dataBlks.end()) {
                if (s < 60) {
                    it->second.secondData[s].spd    = spd;
                    it->second.secondData[s].status = stat;
                    total++;
                }
            } else {// if (param.dataBlks.size() <= n){
                MinusDataBlk blk;
                memset(&blk, 0, sizeof(blk));
                BCDTIME(blk.timeBcd, Y - 2000, M, D, h, m, 0);
                blk.secondData[s].spd    = spd;
                blk.secondData[s].status = stat;
                param.dataBlks[key] = blk;
                total++;
            }
        }
    }
    fclose(fp);
    return param.encode(data, n * 60);
}
struct Loc {
    my::uchar  timeBcd[6];
    my::uint   lng;
    my::uint   lat;
    my::ushort alt;
    my::uchar  spd;
} __attribute__((packed));
struct LocDataBlk {
    std::map<int, Loc> loc;
};
struct X09 {
    std::map<uint64_t, LocDataBlk, greater<uint64_t>> dataBlks;
    bool encode(my::string & msg, int maxNum)
    {
        if (!dataBlks.size()) {
            return false;
        }

        msg << my::hton;
        for (auto it : dataBlks) {
            if (!maxNum){
                break;
            }
            maxNum--;
            msg.append((const char *)it.second.loc.begin()->second.timeBcd, 6);
            int count = 60;
            for (auto l : it.second.loc) {
                msg << l.second.lng;
                msg << l.second.lat;
                msg << l.second.alt;
                msg << l.second.spd;
                logd("%lx : lng %d, lat %d, alt %d, spd %d", it.first, l.second.lng, l.second.lat, l.second.alt, l.second.spd);
                count--;
            }
            if (count) {
                struct Loc pad;
                memset(&pad, 0, sizeof(pad));
                while (count--) {
                    msg << pad.lng;
                    msg << pad.lat;
                    msg << pad.alt;
                    msg << pad.spd;
                }
            }
        }
        return true;
    }
};
bool X09FakeDataParse(const char * path, my::string & data, my::uchar bgn[6], my::uchar end[6], int n)
{
    FILE * fp = fopen(path, "r");
    if (!fp) return false;
    char line[1024];
    struct X09 param;
    int total = 0;
    while(fgets(line, sizeof(line), fp)) {
        uint32_t Y, M, D, h, m, s, ms, spd;
        float lng, lat, alt;
        //开始时间    ; 经度信息; 纬度信息; 高度信息; 速度信息
        //2020-05-31 08:01:00.000,115.0,39.0,37.0,38
        int ret = sscanf(line, "%d-%d-%d %d:%d:%d.%d,%f,%f,%f,%d",
            &Y, &M, &D, &h, &m, &s, &ms, &lng, &lat, &alt, &spd);
        //logd("%d-%d-%d %d:%d:%d.%d,%f,%f,%f,%d; ret = %d", Y, M, D, h, m, s, ms, lng, lat, alt, spd, ret);
        if (11 == ret) {
            my::uchar timeBcd[6];
            BCDTIME(timeBcd, Y - 2000, M, D, h, 0, 0);
            if (memcmp(timeBcd, bgn, 4) < 0 ||
                memcmp(timeBcd, end, 4) > 0) {
                //logd("bgn = %lx end = %lx, cur = %lx", *((uint64_t*)bgn), *((uint64_t*)end), *((uint64_t*)timeBcd));
                continue;
            }
            uint64_t key = 0;
            TIMEKEY(((my::uchar*)&key), Y - 2000, M, D, h, 0, 0);
            auto it = param.dataBlks.find(key);
            struct Loc l;
            BCDTIME(l.timeBcd, Y - 2000, M, D, h, m, 0);
            l.lng = (my::uint)(lng * 60 * 10000);
            l.lat = (my::uint)(lat * 60 * 10000);
            l.alt = (my::ushort)alt;
            l.spd = (my::uchar)spd;
            if (it != param.dataBlks.end()) {
                if (m < 60) {
                    it->second.loc[m] = l;
                    //logd("%lx : lng %d, lat %d, alt %d, spd %d", key, l.lng, l.lat, l.alt, l.spd);
                    total++;
                }
            } else {
                LocDataBlk blk;
                blk.loc[m] = l;
                //logd("%lx : lng %d, lat %d, alt %d, spd %d", key, l.lng, l.lat, l.alt, l.spd);
                param.dataBlks[key] = blk;
                total++;
            }
        }
    }
    fclose(fp);
    return param.encode(data, n);
}
struct AcdDataBlk {
    //my::uchar timeBcd[6]; using first rec
    my::string drvCert;//驾驶证号码
    struct {
        my::uchar timeBcd[6];
        my::uint   lng;
        my::uint   lat;
        my::ushort alt;
        my::uchar spd;
        my::uchar stat1stByte;
    } __attribute__((packed)) rec[100];
    /*using first rec
    my::uint   lng;
    my::uint   lat;
    my::ushort alt;*/
}__attribute__((packed));

struct X10 {
    std::map<uint64_t, AcdDataBlk, greater<uint64_t>> dataBlks;
    bool encode(my::string & msg, int maxNum)
    {
        if (!dataBlks.size()) {
            return false;
        }

        msg << my::hton;
        for (auto it : dataBlks) {
            if (!maxNum){
                break;
            }
            msg.append((const char *)it.second.rec[0].timeBcd, sizeof(it.second.rec[0].timeBcd));
            msg.append(it.second.drvCert);
            maxNum--;
            for (int i = 0; i < ARRAY_SIZE(it.second.rec); i++) {
                msg << it.second.rec[i].spd;
                msg << it.second.rec[i].stat1stByte;
            }
            msg << it.second.rec[0].lng;
            msg << it.second.rec[0].lat;
            msg << it.second.rec[0].alt;
        }
        return true;
    }
};
bool X10FakeDataParse(const char * path, my::string & data, my::uchar bgn[6], my::uchar end[6], int n)
{
    FILE * fp = fopen(path, "r");
    if (!fp) return false;
    char line[1024];
    struct X10 param;
    int total = 0;
    while(fgets(line, sizeof(line), fp)) {
        uint32_t Y, M, D, h, m, s, ms, spd, stat;
        float lng, lat;
        int alt;
        char cert[128] = {0};
        //结束时间,机动车驾驶证号码,开始时间速度信息,信号状态,开始时间经度信息,开始时间纬度信息,开始时间高度信息
        //2020-06-01 06:59:59.800,430224198812247111,32,9C,116.0,40.0,37
        int ret = sscanf(line, "%d-%d-%d %d:%d:%d.%d,%18s,%d,%02x,%f,%f,%d",
            &Y, &M, &D, &h, &m, &s, &ms, cert, &spd, &stat, &lng, &lat, &alt);
        //logd("%d-%d-%d %d:%d:%d.%d,%s,%d,%02x,%f,%f,%d; ret = %d",
        //    Y, M, D, h, m, s, ms, cert, spd, stat, lng, lat, alt, ret);
        if (13 == ret) {
            my::uchar timeBcd[6];
            BCDTIME(timeBcd, Y - 2000, M, D, h, 0, 0);
            if (memcmp(timeBcd, bgn, 6) < 0 ||
                memcmp(timeBcd, end, 6) > 0) {
                logd("bgn = %lx end = %lx, cur = %lx", *((uint64_t*)bgn), *((uint64_t*)end), *((uint64_t*)timeBcd));
                continue;
            }
            int idx = 100 - ((s * 10 + ms / 100) % 200 / 2 + 1);
            uint64_t key = 0;
            TIMEKEY(((my::uchar*)&key), Y - 2000, M, D, h, m, 0);
            auto it = param.dataBlks.find(key);
            if (it != param.dataBlks.end()) {
                if (0 <= idx && idx < 100) {
                    BCDTIME(it->second.rec[idx].timeBcd, Y - 2000, M, D, h, m, s);
                    it->second.rec[idx].spd = spd;
                    it->second.rec[idx].stat1stByte = stat;
                    it->second.rec[idx].lng = (my::uint)(lng * 60 * 10000);;
                    it->second.rec[idx].lat = (my::uint)(lat * 60 * 10000);;
                    it->second.rec[idx].alt = alt;
                    logd("[%03d] %d-%d-%d %d:%d:%d.%d,%s,%d,%02x,%f,%f,%d", idx,
                        Y, M, D, h, m, s, ms, cert, spd, stat, lng, lat, alt);
                    total++;
                }
            } else {
                AcdDataBlk blk;
                if (0 <= idx && idx < 100) {
                    blk.drvCert = cert;
                    BCDTIME(blk.rec[idx].timeBcd, Y - 2000, M, D, h, m, s);
                    blk.rec[idx].spd = spd;
                    blk.rec[idx].stat1stByte = stat;
                    blk.rec[idx].lng = (my::uint)(lng * 60 * 10000);;
                    blk.rec[idx].lat = (my::uint)(lat * 60 * 10000);;
                    blk.rec[idx].alt = alt;
                    logd("[%03d] %d-%d-%d %d:%d:%d.%d,%s,%d,%02x,%f,%f,%d", idx,
                        Y, M, D, h, m, s, ms, cert, spd, stat, lng, lat, alt);
                    param.dataBlks[key] = blk;
                    total++;
                }
            }
        }
    }
    fclose(fp);
    return param.encode(data, n);
}

struct OvTmDataBlk {
    my::string drvCert;//驾驶证号码
    my::uchar bgnTmBcd[6];
    my::uchar endTmBcd[6];
    struct {
        my::uint   lng;
        my::uint   lat;
        my::ushort alt;
    } __attribute__((packed)) BELoc[2];
} __attribute__((packed));

struct X11 {
    std::map<uint64_t, OvTmDataBlk, greater<uint64_t>> dataBlks;
    bool encode(my::string & msg, int maxNum)
    {
        if (!dataBlks.size()) {
            return false;
        }

        msg << my::hton;
        for (auto it : dataBlks) {
            if (!maxNum){
                break;
            }
            maxNum--;
            msg.append(it.second.drvCert);
            msg.append((const char *)it.second.bgnTmBcd, 6);
            msg.append((const char *)it.second.endTmBcd, 6);
            for (int i = 0; i < ARRAY_SIZE(it.second.BELoc); i++) {
                msg << it.second.BELoc[i].lng;
                msg << it.second.BELoc[i].lat;
                msg << it.second.BELoc[i].alt;
            }
        }
        return true;
    }
};
bool X11FakeDataParse(const char * path, my::string & data, my::uchar bgn[6], my::uchar end[6], int n)
{
    FILE * fp = fopen(path, "r");
    if (!fp) return false;
    char line[1024];
    struct X11 param;

    while(fgets(line, sizeof(line), fp)) {
        uint32_t bY, bM, bD, bh, bm, bs;
        uint32_t eY, eM, eD, eh, em, es;
        float bLng, bLat, bAlt;
        float eLng, eLat, eAlt;
        char cert[128] = {0};

        //机动车驾驶证号码,开始时间,结束时间,开始时间经度信息,开始时间纬度信息,开始时间高度信息,结束时间经度信息,结束时间纬度信息,结束时间高度信息
        //430224198812247111,2019-08-24 07:46:08,2019-08-24 12:20:13,115.55739666666666,39.92173833333333,37.0,115.52885,39.89319166666667,39.0
        int ret = sscanf(line, "%18s,%d-%d-%d %d:%d:%d,%d-%d-%d %d:%d:%d,%f,%f,%f,%f,%f,%f",
            cert, &bY, &bM, &bD, &bh, &bm, &bs, &eY, &eM, &eD, &eh, &em, &es, &bLng, &bLat, &bAlt, &eLng, &eLat, &eAlt);
        logd("%18s,%d-%d-%d %d:%d:%d,%d-%d-%d %d:%d:%d,%f,%f,%d,%f,%f,%d",
            cert, bY, bM, bD, bh, bm, bs, eY, eM, eD, eh, em, es, bLng, bLat, bAlt, eLng, eLat, eAlt);
        if (19 == ret) {
            OvTmDataBlk blk;
            blk.drvCert = cert;
            BCDTIME(blk.bgnTmBcd, bY - 2000, bM, bD, bh, bm, bs);
            BCDTIME(blk.endTmBcd, eY - 2000, eM, eD, eh, em, es);
            if (memcmp(blk.bgnTmBcd, bgn, 6) < 0 ||
                memcmp(blk.endTmBcd, end, 6) > 0) {
                logd("bgn = %lx end = %lx, cur = %lx", *((uint64_t*)bgn), *((uint64_t*)end), *((uint64_t*)blk.bgnTmBcd));
                continue;
            }
            blk.BELoc[0].lng = (my::uint)(bLng * 60 * 10000);;
            blk.BELoc[0].lat = (my::uint)(bLat * 60 * 10000);;
            blk.BELoc[0].alt = bAlt;
            blk.BELoc[1].lng = (my::uint)(eLng * 60 * 10000);;
            blk.BELoc[1].lat = (my::uint)(eLat * 60 * 10000);;
            blk.BELoc[1].alt = eAlt;
            uint64_t key = 0;
            TIMEKEY(((my::uchar*)&key), bY - 2000, bM, bD, bh, bm, bs);
            param.dataBlks[key] = blk;
        }
    }
    fclose(fp);
    return param.encode(data, n);
}
struct EvtDataBlk {
    my::uchar  timeBcd[6];
    my::string drvCert;//驾驶证号码
    my::uchar  evtType;//1 登录， 2 退出，其他保留
} __attribute__((packed));

struct X12 {
    std::map<uint64_t, EvtDataBlk, greater<uint64_t>> dataBlks;
    bool encode(my::string & msg, int maxNum)
    {
        if (!dataBlks.size()) {
            return false;
        }

        msg << my::hton;
        for (auto it : dataBlks) {
            if (!maxNum){
                break;
            }
            maxNum--;
            msg.append((const char *)it.second.timeBcd, 6);
            msg.append(it.second.drvCert);
            msg << it.second.evtType;
        }
        return true;
    }
};
bool X12FakeDataParse(const char * path, my::string & data, my::uchar bgn[6], my::uchar end[6], int n)
{
    FILE * fp = fopen(path, "r");
    if (!fp) return false;
    char line[1024];
    struct X12 param;

    while(fgets(line, sizeof(line), fp)) {
        uint32_t bY, bM, bD, bh, bm, bs, evtType = 0;
        char cert[128] = {0};

        //发生时间,机动车驾驶证号码,事件类型
        //2020-05-27 23:23:54,430224198812247111,01
        int ret = sscanf(line, "%d-%d-%d %d:%d:%d,%18s,%d",
            &bY, &bM, &bD, &bh, &bm, &bs, cert, &evtType);
        logd("%d-%d-%d %d:%d:%d,%18s,%x",
            bY, bM, bD, bh, bm, bs, cert, evtType);
        if (8 == ret) {
            EvtDataBlk blk;
            BCDTIME(blk.timeBcd, bY - 2000, bM, bD, bh, bm, bs);
            if (memcmp(blk.timeBcd, bgn, 6) < 0 ||
                memcmp(blk.timeBcd, end, 6) > 0) {
                logd("bgn = %lx end = %lx, cur = %lx", *((uint64_t*)bgn), *((uint64_t*)end), *((uint64_t*)blk.timeBcd));
                continue;
            }
            blk.drvCert = cert;
            blk.evtType = evtType;
            uint64_t key = 0;
            TIMEKEY(((my::uchar*)&key), bY - 2000, bM, bD, bh, bm, bs);
            param.dataBlks[key] = blk;
        }
    }
    fclose(fp);
    return param.encode(data, n);
}
struct ACCDataBlk {
    my::uchar  timeBcd[6];
    my::uchar  acc;//1 通电 2 断电
} __attribute__((packed));

struct X13 {
    std::map<uint64_t, ACCDataBlk, greater<uint64_t>> dataBlks;
    bool encode(my::string & msg, int maxNum)
    {
        if (!dataBlks.size()) {
            return false;
        }

        msg << my::hton;
        for (auto it : dataBlks) {
            if (!maxNum){
                break;
            }
            maxNum--;
            msg.append((const char *)it.second.timeBcd, 6);
            msg << it.second.acc;
        }
        return true;
    }
};
bool X13FakeDataParse(const char * path, my::string & data, my::uchar bgn[6], my::uchar end[6], int n)
{
    FILE * fp = fopen(path, "r");
    if (!fp) return false;
    char line[1024];
    struct X13 param;

    while(fgets(line, sizeof(line), fp)) {
        uint32_t bY, bM, bD, bh, bm, bs, evtType = 0;
        char cert[128] = {0};

        //发生时间,事件类型
        //2020-05-27 01:34:46,01
        int ret = sscanf(line, "%d-%d-%d %d:%d:%d,%x",
            &bY, &bM, &bD, &bh, &bm, &bs, &evtType);
        logd("%d-%d-%d %d:%d:%d,%x",
            bY, bM, bD, bh, bm, bs, evtType);
        if (7 == ret) {
            ACCDataBlk blk;
            BCDTIME(blk.timeBcd, bY - 2000, bM, bD, bh, bm, bs);
            if (memcmp(blk.timeBcd, bgn, 6) < 0 ||
                memcmp(blk.timeBcd, end, 6) > 0) {
                logd("bgn = %lx end = %lx, cur = %lx", *((uint64_t*)bgn), *((uint64_t*)end), *((uint64_t*)blk.timeBcd));
                continue;
            }
            blk.acc = evtType;
            uint64_t key = 0;
            TIMEKEY(((my::uchar*)&key), bY - 2000, bM, bD, bh, bm, bs);
            param.dataBlks[key] = blk;
        }
    }
    fclose(fp);
    return param.encode(data, n);
}
struct SpdStatDataBlk {
    my::uchar stat;//1 正常 2异常
    my::uchar bgnTmBcd[6];
    my::uchar endTmBcd[6];
    struct {
        my::uchar spdRec;
        my::uchar spdRef;
    } __attribute__((packed)) spd[60];
} __attribute__((packed));

struct X15 {
    std::map<uint64_t, SpdStatDataBlk, greater<uint64_t>> dataBlks;
    bool encode(my::string & msg, int maxNum)
    {
        if (!dataBlks.size()) {
            return false;
        }

        msg << my::hton;
        for (auto it : dataBlks) {
            if (!maxNum){
                break;
            }
            maxNum--;
            msg << it.second.stat;
            msg.append((const char *)it.second.bgnTmBcd, 6);
            msg.append((const char *)it.second.endTmBcd, 6);
            for (int i = 0; i < ARRAY_SIZE(it.second.spd); i++) {
                msg << it.second.spd[i].spdRec;
                msg << it.second.spd[i].spdRef;
            }
        }
        return true;
    }
};
bool X15FakeDataParse(const char * path, my::string & data, my::uchar bgn[6], my::uchar end[6], int n)
{
    FILE * fp = fopen(path, "r");
    if (!fp) return false;
    char line[1024];
    struct X15 param;

    while(fgets(line, sizeof(line), fp)) {
        uint32_t stat, bY, bM, bD, bh, bm, bs, bMs;
        uint32_t eY, eM, eD, eh, em, es, eMs, spdRec, spdRef;

        //记录仪的速度状态,开始时间,结束时间,开始时间速度信息,开始时间参考速度
        //01,2020-05-20 07:00:00.000,2020-05-20 07:05:00.000,49,50
        int ret = sscanf(line, "%d,%d-%d-%d %d:%d:%d.%d,%d-%d-%d %d:%d:%d.%d,%d,%d",
            &stat, &bY, &bM, &bD, &bh, &bm, &bs, &bMs, &eY, &eM, &eD, &eh, &em, &es, &eMs, &spdRec, &spdRef);
        logd("%d,%d-%d-%d %d:%d:%d.%d,%d-%d-%d %d:%d:%d.%d,%d,%d",
            stat, bY, bM, bD, bh, bm, bs, bMs, eY, eM, eD, eh, em, es, eMs, spdRec, spdRef);
        if (17 == ret) {
            SpdStatDataBlk blk;
            blk.stat = stat;
            BCDTIME(blk.bgnTmBcd, bY - 2000, bM, bD, bh, bm, bs);
            BCDTIME(blk.endTmBcd, eY - 2000, eM, eD, eh, em, es);
            if (memcmp(blk.bgnTmBcd, bgn, 5) < 0 ||
                memcmp(blk.endTmBcd, end, 5) > 0) {
                logd("bgn = %lx end = %lx, cur = %lx", *((uint64_t*)bgn), *((uint64_t*)end), *((uint64_t*)blk.bgnTmBcd));
                continue;
            }
            uint64_t key = 0;
            TIMEKEY(((my::uchar*)&key), bY - 2000, bM, bD, bh, bm, 0);
            auto it = param.dataBlks.find(key);
            if (it != param.dataBlks.end()) {
                it->second.spd[bs].spdRec = spdRec;
                it->second.spd[bs].spdRef = spdRef;
            } else {
                blk.spd[bs].spdRec = spdRec;
                blk.spd[bs].spdRef = spdRef;
                param.dataBlks[key] = blk;
            }
        }
    }
    fclose(fp);
    return param.encode(data, n);
}

bool JttClient::fake8700(X8700 req, const my::constr data, const my::string& sim, my::ushort sn)
{
    my::datetime tick;
    my::uchar cmd;
    my::uchar bgn[6] = { 1, 1, 1,  0,  0,  0};
    my::uchar end[6] = {33, 1, 1, 23, 59, 59};
    my::ushort num = 0;
    const char * p = (const char *)data;

    cmd = p[0];
    p += 7;
    if (req.msg.length() > 15) {
        // 开始时间
        memcpy(bgn, p, 6);
        p += 6;
        // 结束时间
        memcpy(end, p, 6);
        p += 6;
        // 数量
        num = ((my::ushort)p[0] << 16) | p[1];
        logd("num = %d!", num);
    } else {
        switch (cmd) {
            case 0x08:
            case 0x15:
                num = 2880;
                break;
            case 0x09:
                num = 48;
                break;
            default:
                num = 1;
                break;
        }
        logd("use default param!");
    }
    Current st = ServiceHelper::getInstance().getStatus();
    char timeBCD[6];
    numstr2bcd(timeBCD, my::timestamp::YYMMDD_HHMMSS(), sizeof(timeBCD) * 2);
    X0700 resp;
    resp.seq = sn;
    resp.cmd = req.cmd;
    char path[128];
    snprintf(path, sizeof(path), "/data/%02xH.txt", req.cmd);

    switch(req.cmd)
    {
        case 0x08: {
            std::vector<my::string> data;
            if (!X08FakeDataParse(path, data, bgn, end, num)) {
                return false;
            }
            JttMsg msg = resp.encode(sim, mProtVersion, data);

            if (!send(msg)) {
                logw("%s > Failed to send X0700 response.", __FUNCTION__);
            }
            return true;
        }
        case 0x09: {
            if (!X09FakeDataParse(path, resp.msg, bgn, end, num)) {
                return false;
            }
            break;
        }
        case 0x10: {
            if (!X10FakeDataParse(path, resp.msg, bgn, end, num)) {
                return false;
            }
            break;
        }
        case 0x11: {
            if (!X11FakeDataParse(path, resp.msg, bgn, end, num)) {
                return false;
            }
            break;
        }
        case 0x12: {
            if (!X12FakeDataParse(path, resp.msg, bgn, end, num)) {
                return false;
            }
            break;
        }
        case 0x13:
        case 0x14: {
            if (!X13FakeDataParse(path, resp.msg, bgn, end, num)) {
                return false;
            }
            break;
        }
        case 0x15: {
            if (!X15FakeDataParse(path, resp.msg, bgn, end, num)) {
                return false;
            }
            break;
        }
        default:
            return false;
    }

    JttMsg msg = resp.encode(sim, mProtVersion);

    if (!send(msg)) {
        logw("%s > Failed to send X0700 response.", __FUNCTION__);
    }
    return true;
}
#endif

bool JttClient::proc8700(const my::string& sim, my::ushort sn, const my::constr& data)
{
    Manager & m = Manager::getInstance();
    X8700 req;

    if (!req.decode(data)) {
        logw("%s > Failed to decode, msg={\n%s\n}.", __FUNCTION__, my::hex(data, true).c_str());
        // 回复通用应答: 消息有误
        reply(sim, 0x8700, sn, 2);
        return false;
    }
#ifdef FAKE_8700
    if (fake8700(req, data, sim, sn)) {
        return true;
    }
#endif
    logw("%s > msg={\n%s\n}.", __FUNCTION__, my::hex(req.msg, true).c_str());
    m.sendMcuMsg(MCU_MSG_TYPE_MCU_GB19056_FETCH, (uint8_t *)((const char*)data), data.length());
    Current st = ServiceHelper::getInstance().getStatus();
    char timeBCD[6];
    numstr2bcd(timeBCD, my::timestamp::YYMMDD_HHMMSS(), sizeof(timeBCD) * 2);
    X0700 resp;
    resp.seq = sn;
    resp.cmd = req.cmd;

    int trycnt = 3;
    char fileName[128];
    snprintf(fileName, sizeof(fileName), "/mnt/obb/.%02xH.dat", resp.cmd);

    while (trycnt-- > 0) {
        usleep(1e5);
        FILE * fp = fopen(fileName, "r");

        if (fp) {
            fseek(fp, 0, SEEK_END);
            int file_size = ftell(fp);
            resp.msg.length(file_size);
            rewind(fp);

            if (file_size != fread((void*)resp.msg.c_str(), 1, file_size, fp)) {
                loge("%s > read %s error!", __FUNCTION__, fileName);
            }

            fclose(fp);
            break;
        }
    }

    JttMsg msg = resp.encode(sim, mProtVersion);

    if (!send(msg)) {
        logw("%s > Failed to send X0700 response.", __FUNCTION__);
        return false;
    }

    return true;
}

// 文件上传指令
bool JttClient::proc9206(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X9206 req;

    if (!req.decode(data)) {
        logw("[0x9206] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        // 回复通用应答: 消息有误
        reply(sim, 0x9206, sn, 2);
        return false;
    }

    reply(sim, 0x9206, sn, 0);

    logd("[0x9206] ftp file a upload media request. ch %d, beginTs %x%x%x%x%x%x, end %x%x%x%x%x%x", req.ch,
            req.s_time[0], req.s_time[1], req.s_time[2], req.s_time[3], req.s_time[4], req.s_time[5],
            req.e_time[0], req.e_time[1], req.e_time[2], req.e_time[3], req.e_time[4], req.e_time[5]);

    logd("ftp param: ip:%s, port:%d, user:%s, passwd:%s, path:%s", req.server_ip.c_str(), req.server_port, req.user.c_str(), req.password.c_str(), req.path.c_str());
    auto it = mFtpTasks.begin();
    while (it != mFtpTasks.end()) {
        shared_ptr<JttFtpUpload> sp = it->second;
        X9206 hr = sp->getReqInfo();
        if ((hr.server_ip == req.server_ip) &&
            (hr.server_port == req.server_port) &&
            (hr.ch == req.ch) &&
            (!memcmp(hr.s_time, req.s_time, sizeof(hr.s_time))) &&
            (!memcmp(hr.e_time, req.e_time, sizeof(hr.e_time))) &&
            (hr.alarm_tag == req.alarm_tag) &&
            (hr.media == req.media) &&
            (hr.code == req.code) &&
            (hr.storage == req.storage)
        ) {
            break;
        }
        it++;
    }
    if (it == mFtpTasks.end()) {
        shared_ptr<JttFtpUpload> sp = make_shared<JttFtpUpload>(this, req, sim, sn);
        if (sp.get()) {
            sp->start();
            mFtpTasks[sn] = sp;
        }
    } else {
        loge("Last same task is runing!");
        return false;
    }
    return true;
}

// 文件上传控制
bool JttClient::proc9207(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X9207 req;
    if (!req.decode(data)) {
        logw("[0x9207] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        // 回复通用应答: 消息有误
        reply(sim, 0x9207, sn, 2);
        return false;
    }

    logd("[0x9207]: set sn %d ftp status %d", req.sn, req.ctl);
    auto it = mFtpTasks.find(req.sn);
    if (it != mFtpTasks.end()) {
        shared_ptr<JttFtpUpload> ftp = it->second;
        ftp->setFtpStatus(req.ctl);
        logd("[0x9207]: find sn %d in ftp tasks", req.sn);
    } else {
        logd("[0x9207]: not find sn %d in ftp tasks", req.sn);
    }
    reply(sim, 0x9207, sn, 0);
    return true;
}

// 报警附件上传指令
bool JttClient::proc9208(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X9208 req;

    if (!req.decode(data, mProtVersion, prot_subtype)) {
        logw("[0x9208] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        // 回复通用应答: 消息有误
        reply(sim, 0x9208, sn, 2);
        return false;
    }
    logd("req.server_ip %s, %d", req.server_ip.c_str(), req.tcp_port);
    char res = 0;

    AlarmInfoItem alarm_info;
    alarm_info.protVer = (com_service->si.attSndProtVer < 0) ? mProtVersion : com_service->si.attSndProtVer;
    alarm_info.prot_subtype = prot_subtype;
    alarm_info.server_ip = req.server_ip;
    alarm_info.tcp_port = req.tcp_port;
    alarm_info.udp_port = req.udp_port;
    alarm_info.sim = sim;
    DbHelper & dbh = DbHelper::getInstance();
    int ret = dbh.getAlarmInfoByTag(req.alarm_tag, alarm_info);

    //logd("#### 0x9208 ver=%d server_ip=%s, tcp_port=%d,term_id=%s, cnt=%d,sn=%d,alarm_id=%s", mProtVersion,
    //  alarm_info.server_ip.c_str(), alarm_info.tcp_port,
    //  alarm_info.alarm_tag.term_id.c_str(), alarm_info.alarm_tag.cnt, alarm_info.alarm_tag.sn, req.alarm_id);

    if (ret) {
        res = 1;

    } else {
        logd("put attachemnt: %s", alarm_info.to_string(false, "a").c_str());
        alarm_info.alarm_id.assign(req.alarm_id, sizeof(req.alarm_id));
        send_attachment(alarm_info);
    }

    reply(sim, 0x9208, sn, res);
    return true;
}

// 文件上传完成消息应答
bool JttClient::proc9212(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X9212 req;

    if (!req.decode(data)) {
        logw("[0x9212] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        // 回复通用应答: 消息有误
        reply(sim, 0x9212, sn, 2);
        return false;
    }

    logd("[0x9212] file=[%s], res=[%d]", req.path.c_str(), req.res);
    reply(sim, 0x9212, sn, 0);

    return true;
}
void JttClient::onMessageReceived(const std::shared_ptr<minieye::AMessage> &msg) {
    if (onMsgRcved(msg)) {
        return;
    }
    switch(msg->what()) {
        case EVT_TYPE_PROT_DB_SAVE_RPT_DATA: {
            if (!access("/data/disable_save_rpt_data", R_OK)) {
                logd("disable save rpt data by /data/disable_save_rpt_data!s");
                break;
            }
            // 盲区数据
            minieye::AString tmp;
            int32_t tmS = 0;
            msg->findString("data", &tmp);
            msg->findInt32("time", &tmS);

            ReportData rd;
            rd.data.assign(tmp.c_str(), tmp.size());

            DbHelper &dbHelper = DbHelper::getInstance();
            LOG_IF(dbHelper.addReportData(rd, this->tag, tmS) != 0, logw, "Failed to add report data.");
            break;
        }
        case EVT_TYPE_PROT_DB_CHECK_RPT_DATA: {
            // 检测盲区补报
            LOG_IF(check_report_data() != 0, logw, "[JttTcpClient:run] failed to check report data.");
            break;
        }
        case EVT_TYPE_PROT_DB_CHECK_AREA_ALARM: {
            // 检测预警
            int r = check_alarm();
            LOG_IF(r != 0, logw, "[JttTcpClient:run] Failed to check alarm.");
            break;
        }
        default: {

        }
    }
}

bool JttAtTskMgr::proc(AlarmInfoItem & alarm_info, bool bWait)
{
    Manager & m = Manager::getInstance();
    ServiceHelper & sh = ServiceHelper::getInstance();
    DbHelper & dh = DbHelper::getInstance();
    int ret = 0;

#ifndef _MSC_VER
    //check media files
    std::vector<AlarmInfoAtItem>::iterator it = alarm_info.attachment.begin();

    for (; it != alarm_info.attachment.end(); it++) {

        if (access(it->path.c_str(), F_OK)) {
            if (bWait) {
                loge("JttAtTskMgr wait file %s\n", it->path.c_str());
                return false;

            } else {
                m.mpLogger->mlog("attachment fail! : %s!", it->path.c_str());
            }
        }
    }

    my::string sim = alarm_info.sim;
    JttTcpClient client(alarm_info.server_ip, alarm_info.tcp_port);
    ret = client.connect(true, 5000);
    LOG_RETURN_IF(ret != 0, false, loge, "[AtMgrTsk] failed to connect ip=[%s], port=[%d]", alarm_info.server_ip.c_str(), alarm_info.tcp_port);

    X1210 x1210;
    ret = sh.generateX1210(x1210, alarm_info);
    LOG_RETURN_IF(ret != 0, false, loge, "[AtMgrTsk] failed to generate x1210.");
    bool bNew = (alarm_info.protVer == 1) ? true : false;
    JttMsg msg = x1210.encode(sim, bNew, alarm_info.prot_subtype);
    LOG_RETURN_IF(!client.send(msg), false, loge, "[AtMgrTsk] Failed to send X1210 response.");
    ret = client.check_res(bNew);
    if (access("/data/not_check_1210_rsp", R_OK)) {
        LOG_RETURN_IF(ret != 0, false, loge, "[AtMgrTsk] Failed to check X1210 response.");
    } else {
        LOG_IF(ret != 0, loge, "[AtMgrTsk] Failed to check X1210 response.");
    }
    for (auto it = x1210.attachment.begin(); it != x1210.attachment.end();) {
        logi("[AtMgrTsk] start to send attachement, file=[%s], size=[%d]", it->path.c_str(), it->size);
        X1211 x1211;
        x1211.path = it->file;
        x1211.size = it->size;
        if (strstr(it->file.c_str(), "mp4")) {
            x1211.type = MM_VIDEO;
        } else if (strstr(it->file.c_str(), "jpg")) {
            x1211.type = MM_PHOTO;
        } else if (strstr(it->file.c_str(), "bin")) {
            x1211.type = MM_BIN;
        } else {
            x1211.type = MM_AUDIO;
        }
        msg = x1211.encode(sim, bNew);
        LOG_RETURN_IF(!client.send(msg), false, loge, "[AtMgrTsk] Failed to send X1211 response.");
        ret = client.check_res(bNew);
        if (access("/data/not_check_1211_rsp", R_OK)) {
            LOG_RETURN_IF(ret != 0, false, loge, "[AtMgrTsk] Failed to check X1211 response.");
        } else {
            LOG_IF(ret != 0, loge, "[AtMgrTsk] Failed to check X1211 response.");
        }
        // 上传文件信息
        ret = sh.uploadFileData(&client,
                it->path,
                it->file);

        if (ret != 0) {
            logw("[AtMgrTsk] Failed to upload file data=[%s]", it->path.c_str());
            it++;
            continue;
        }

        // 完成一个文件信息
        X1212 x1212;
        x1212.path = it->file;
        x1212.size = it->size;
        if (strstr(it->file.c_str(), "mp4")) {
            x1212.type = MM_VIDEO;
        } else if (strstr(it->file.c_str(), "jpg")) {
            x1212.type = MM_PHOTO;
        } else if (strstr(it->file.c_str(), "bin")) {
            x1212.type = MM_BIN;
        } else {
            x1212.type = MM_AUDIO;
        }
        msg = x1212.encode(sim, bNew);
        LOG_IF(!client.send(msg), loge, "[AtMgrTsk] Failed to send X1212 response.");

        X9212 x9212;
        ret = client.check_9212(bNew, x9212);
        if (ret == 0) {
            logi("[AtMgrTsk] attachement was uploaded, file=[%s], size=[%d]", it->path.c_str(), it->size);
        } else if (ret == 1){
            for (auto it1 = x9212.data_list.begin(); it1 != x9212.data_list.end(); it1++) {
                sh.uploadFileOffset(&client, it->path, it->file, it1->offset, it1->len);
            }
            // 补传完成后 再发一次文件上传完成消息1212
            LOG_IF(!client.send(msg), loge, "[AtMgrTsk] Failed to send X1212 response.");
        } else if (ret == -1) {
            logi("[AtMgrTsk] X9212 decode failed, file=[%s], size=[%d]", it->path.c_str(), it->size);
        }
        
        it = x1210.attachment.erase(it);
    }

#endif
    return true;
}
// 开启TCP客户端
int JttTcpClient::start(const char* ip, my::ushort port, const char* ip2, my::ushort port2)
{
    LOG_RETURN_IF(!com_service, -1, loge, "[JttTcpClient::start] Com Service is empty.");
    this->ip = ip;
    this->port = port;
    this->ip2 = ip2;
    this->port2 = port2;

    mode = 0;
    retry_connect = 0;
    authorize_retry_connect = 0;


    return thread::start();
}

// 重新加载客户端
int JttTcpClient::reload(const char* ip, my::ushort port, const char* ip2, my::ushort port2)
{
    if (!mode) {
        if (strcmp(this->ip.c_str(), ip) || this->port != port) {
            this->stop();

            if (strlen(ip) != 0) {
                return this->start(ip, port, ip2, port2);
            }
        }

    } else {
        if (strcmp(this->ip2.c_str(), ip2) || this->port2 != port2) {
            this->stop();

            if (strlen(ip2) != 0) {
                return this->start(ip, port, ip2, port2);
            }
        }
    }

    this->ip = ip;
    this->port2 = port;
    this->ip2 = ip2;
    this->port2 = port2;
    return 0;
}

// 停止客户端
void JttTcpClient::stop()
{
    my::thread::stop();
    c.close();
    //com_service = NULL;
}


// TCP连接, 阻塞式
int JttTcpClient::connect(bool block, int ms)
{
    // 先断再连
    c.close();
    if (!access("/data/minieye/sms_config_get", R_OK)) {
        if (mLastDisconnectTsMs.elapsed() > 5 * 60 * 1000) {
            unlink("/data/minieye/sms_config_get");
            logd("%f\n", mLastDisconnectTsMs.elapsed());
        } else {
            logd("%f\n", mLastDisconnectTsMs.elapsed());
            return -1;
        }
    }

    if (mbDummy) {
        check_alarm(false);
        logd("tag %s connect dummy", tag.c_str());
        return -1;
    }

    my::string active_ip = mode ? ip2 : ip;
    my::ushort active_port = mode ? port2 : port;

    logi("[JttTcpClient::connect] Begin to connect to %s:%d.", active_ip.c_str(), active_port);

    if (c.connect(active_ip.c_str(), active_port, 5000) < 0) {
        retry_connect++;
        if (retry_connect > tcp_retry_max) {
            if (!ip2.empty()) {
                mode = !mode; //改变主备模
                retry_connect = 0;
            } else {
                retry_connect = tcp_retry_max;
            }
        }
        loge("connect fail! retry_connect %d, %s", retry_connect, strerror(errno));
        return -1;
    } else {
        retry_connect = 0;
    }

    // 设置recv非阻塞+超时
    my::net::set_mode(c.fd, block);
    my::net::set_timeout(c.fd, my::net::ip::rx, ms);
    my::net::set_timeout(c.fd, my::net::ip::tx, ms);

    return 0;
}
bool JttTcpClient::chkOnline(Current & st)
{
    Manager & m = Manager::getInstance();
    my::uint ts = (my::uint)my::timestamp::now();
    if (!(mProtState & JttClient::AUTHORIZED)) {
        //保存位置信息
        if (ts >= expiries[3]) {
            m.mpLogger->mlog("%s, chkOnline update expiries[3] = %d", tag.c_str(), expiries[3]);
            expiries[3] = ts + timeout[3];
            int ret = save(st);
            LOG_IF(ret, logw, "Failed to save schedule location, time=[%d], lat=[%3.3f], lng=[%3.3f]", ts, st.lbs.lat, st.lbs.lng);
            if (1 != st.lbs.status) {
                logd("Gps Can't locate now!");
            }
        } else {
            //logd("ts %d expiries[3] %d", ts, expiries[3]);
        }
    }
    // 如果尚未TCP连接上
    if (!(mProtState & JttClient::ONLINE)) {
        //尝试连接
        if (ts >= expiries[0]) { // 如果重试时间到
            logi("Current mProtState=[%d], expiry=[%d, %d, %d], timer=[%d, %d].", mProtState, expiries[0], expiries[1], expiries[2], timer.size, (int)timer.data.size());

            if (!connect()) { // 如果连接成功
                logi("Connect to %s:%d successfully.", (!mode ? ip : ip2).c_str(), !mode ? port : port2);
                mProtState |= JttClient::ONLINE;
                expiries[0] = ts + timeout[0];

            } else {
                int tmOut = 1;
                for (int i = 0; i < retry_connect; i++) {
                    tmOut *= (i + 1);
                }
                tmOut *= timeout[0];
                logi("Failed to connect to %s:%d, try again in %d seconds.", (!mode ? ip : ip2).c_str(), !mode ? port : port2, tmOut);
                expiries[0] = ts + tmOut; // 更新下一次重试时间
            }
        } else {
            int diff = expiries[0] - ts;
            if (!(diff % 10)) {
                logd("ts = %d, expire %d", ts, expiries[0]);
            }
        }
    } else if (mOnlineTs.elapsed() >= 3000){
        mOnlineTs = my::timestamp::now();
        char value[PROP_VALUE_MAX];
        memset(value, 0, sizeof(value));
        snprintf(value, sizeof(value), "%d", (my::uint)my::timestamp::now());
        __system_property_set(PROP_RW_MINIEYE_PROT_ONLINE_TS, value);
    }
    return (mProtState & JttClient::ONLINE);
}

bool JttTcpClient::chkRegStat()
{
    if (!com_service->si.auth.length()) {
        // 如果尚未注册
        if (!(mProtState & JttClient::JttREGISTERED)) {
            if ((my::uint)my::timestamp::now() >= expiries[1]) { // 如果到了重试时间
                expiries[1] = (my::uint)my::timestamp::now() + (!!reg_retry ? 30 : timeout[1]);
                reg_retry--;
                if (!reg_retry) {
                    reg_retry = 3;
                }
                logi("[JttTcpClient:run] Begin to register.");
                enregister(); // 发起注册
            }
        }
    } else {
        mProtState |= JttClient::JttREGISTERED;
    }
    return (mProtState & JttClient::JttREGISTERED);
}

bool JttTcpClient::chkSMS()
{
    Manager & m = Manager::getInstance();
    if (!access("/data/minieye/sms_config_get", R_OK)) {
        mProtState = JttClient::INVALID;
        expiries[0] = 0; // 立刻重连
        expiries[1] = 0; // 立刻重连
        resetAuthed(); // 立刻重连
        clear();
        mLastDisconnectTsMs = my::timestamp::now();
        c.close();
        /*7E 00 02 00 40 01 00 00 00 00 00 23 66 71 97 49 00 44 ED 7E*/
        char telNum[PROP_VALUE_MAX] = {0};
        __system_property_get(PROP_PERSIST_MINIEYE_SMS_SUM, telNum);
        string cmd = "send_sms.sh ";
        cmd += telNum;
        cmd += " 'MINIEYE : ";
        cmd += sim;
        cmd += " is disconnected.'";
        system(cmd.c_str());
        m.mpLogger->mlog("send sms : %s", cmd.c_str());
        return false;
    }
    return true;
}
bool JttTcpClient::chkAuthed()
{
    // 如果尚未授权
    if (!(mProtState & JttClient::AUTHORIZED)) {
        if ((my::uint)my::timestamp::now() >= expiries[2]) { // 如果到了重试时间
            int tmOut = 1;
            if (authorize_retry_connect) {

                for (int i = 0; i <= authorize_retry_connect; i++) {
                    tmOut *= (i + 1);
                }
                tmOut *= timeout[2];

            } else {
                /* 第一次连接超时时间为TCP/UDP超时时间 */
                tmOut = timeout[2];
            }

            expiries[2] = (my::uint)my::timestamp::now() + tmOut;

            if (authorize_retry_connect > tcp_retry_max) {
                resetAuthed();

                if (!ip2.empty()) {

                    mode = ((mode == 0) ? 1 : 0); //改变主备模
                    mProtState = JttClient::INVALID;
                    expiries[0] = 0; // 立刻重连
                    expiries[1] = 0; // 立刻重连
                    resetAuthed(); // 立刻重连
                    clear();
                    logw("[JttTcpClient:run]  authorize_retry_connect=%d,mode=%d", authorize_retry_connect, mode);
                }
            }
            logi("[JttTcpClient:run] Begin to authorize.");
            authroize(); // 发起注册
            authorize_retry_connect++;
        }
    } else {
        authorize_retry_connect = 0;
        std::string flag = "/data/minieye/";
        flag += tag + "_upgrade_ok";
        if (!access(flag.c_str(), R_OK)) {
            X0108 rsp;
            JttMsg msg = rsp.encode(sim, 0, mProtVersion);
            if (!send(msg)) {
                loge("failed to send response 0x0108");
                return false;
            }
            std::string cmd = "rm -f ";
            cmd += flag;
            system(cmd.c_str());
        }
    }
    return (mProtState & JttClient::AUTHORIZED);
}
int JttTcpClient::gnssSnd()
{
    Manager & m = Manager::getInstance();
    my::string gnssData;
    int seek = timeout[7];
    seek += !seek;
    if (m.state_controller->getCurGNSS(gnssData, -seek) > 0) {
        X0900 req;
        req.type = 0;
        req.data[0] = gnssData;
        JttMsg msg = req.encode(sim, mProtVersion);
        if (!send(msg)) {
            loge("[X0900] failed to send  to server.");
            return -2;
        } else {
            logd("[X0900] success to send to server.");
        }
    } else {
        //logd("get gnss failed!\n");
        return -1;
    }
    return 0;
}

int JttTcpClient::gnssRpt()
{
    Manager & m = Manager::getInstance();
    Config & config = m.config;

    my::uint now = (my::uint)my::timestamp::now();
    if ((1 == config.sys.gnss.detail_upmode) && (now >= expiries[7])) {
        expiries[7] = now + timeout[7];
        gnssSnd();
    } else {
        //logd("upmode:%d, now:%d, expiries[7]:%d\n",config.sys.gnss.detail_upmode, now, expiries[7]);
    }
    return 0;
}

bool JttTcpClient::chkRptCond(Current::AlarmBits * curAlarm, Current::AlarmBits * lastAlarm, Current::AlarmBits * chkAlarm,
    Current::StateBits * curState, Current::StateBits * lastState)
{
    if (//(curState->acc != lastState->acc) ||
        (curAlarm->pwrDWN != lastAlarm->pwrDWN)||
        //(!curAlarm->tiredDrv && lastAlarm->tiredDrv) ||
        (curAlarm->speedingAlarm != lastAlarm->speedingAlarm) ||
        (curAlarm->speeding != lastAlarm->speeding)||
        //(curAlarm->parkTmOv != lastAlarm->parkTmOv)||
        (curAlarm->unlawDrvAlarm != lastAlarm->unlawDrvAlarm) ||
        ((curAlarm->emergTrig != lastAlarm->emergTrig) && !chkAlarm->emergTrig)) {
        return true;
    }
    return false;
}

// 上报位置
int JttTcpClient::locationRpt(Current & st)
{
    Manager & m = Manager::getInstance();
    Config & config = m.config;
    //my::uint now = (my::uint)my::timeStamp::now();

    struct timespec tmp = {0};
    clock_gettime(CLOCK_MONOTONIC, &tmp);
    my::uint now = (tmp.tv_sec + (tmp.tv_nsec / (1000 * 1000 * 1000)));

    if (trace_expire != 0 && now <= trace_expire && timeout[5] != 0) { // 开启临时位置跟踪, 且有效
        if (now >= expiries[5]) {
            expiries[5] = now + timeout[5];
            int ret = this->report(st);
            LOG_IF(ret, logw, "[JttTcpClient:run] Failed to report trace location, time=[%d], lat=[%3.3f], lng=[%3.3f]", now, st.lbs.lat, st.lbs.lng);
        }

    } else { // 周期上报
        //logd("[JttTcpClient:run] now:%d, expi:%d", now, expiries[3]);
        unsigned int curState = st.getCurState();
        Current::StateBits * sb = (Current::StateBits *)&curState;
        Current::StateBits * sbLast = (Current::StateBits*)&mRptState;

        unsigned int curAlarm = st.getCurAlarm();
        Current::AlarmBits * ab = (Current::AlarmBits *)&curAlarm;
        Current::AlarmBits * abLast = (Current::AlarmBits*)&mRptAlarm;
        Current::AlarmBits * cb = (Current::AlarmBits *)&mbChkAlarmBits;
        
        bool rpt = false;
        if (st.getStateAcc() && cb->emergTrig && !ab->emergTrig) {
            cb->emergTrig = 0;
        }

        bool isSleepMode = false;
        bool distanceRpt = false;
        my::uint accTimeDelay = config.sys.acc_time_off; // 对应config.ini中media.acc.time.off的值
        int32_t acc_off_interval = config.sys.report.acc_off_inteval + !config.sys.report.acc_off_inteval;
        int32_t sleep_interval = config.sys.report.sleep_inteval + !config.sys.report.sleep_inteval;
        int32_t accOffMaxCnt = accTimeDelay / acc_off_interval + !!(accTimeDelay % acc_off_interval);

        if (st.getStateAcc()) {
            mLastSleepMode = false;
            mAccOffTS = my::timestamp::now();
            mSleepMode = 0;
        }

        if (mAccOffTS.elapsed() >= accTimeDelay * 1000) { //acc 断开1min 进入低功耗
            isSleepMode = true;
        } else {
            isSleepMode = false;
        }

        if (st.getStateAcc() != sbLast->acc) { // acc状态发生变化
            if (!st.getStateAcc()) {//acc on - > acc off
                mSleepMode = 1;
                m.mpLogger->mlog("locationRpt acc on -> acc off, report 0200, update expiries[3] = %d", now);
                logd("locationRpt acc off 30s, report 0200");
            } else if (st.getStateAcc()) { //acc off -> acc on
                mSleepMode = 0;
                m.mpLogger->mlog("locationRpt acc off -> acc on, report 0200, update expiries[3] = %d", now);
                logd("locationRpt acc off 30s, report 0200");
            }
            rpt = true;
            expiries[3] = now;
        } else if (mLastSleepMode != isSleepMode && isSleepMode) { // 第一次低功耗模式
            mLastSleepMode = true;
            mSleepMode = 2;
            rpt = true;
            expiries[3] = now;
            m.mpLogger->mlog("locationRpt enter sleepMode, report 0200, update expiries[3] = %d", now);
            logd("locationRpt enter sleepMode, report 0200, update expiries[3] = %d", now);
        } else { // 其它
            if (0 == config.sys.report.mode) { //定时上报
                int32_t inteval = 0;
                if (mSleepMode == 0){
                    inteval = timeout[3];
                }else if (mSleepMode == 1) {
                    inteval = acc_off_interval;
                } else if (mSleepMode == 2) {
                    inteval = sleep_interval;
                }
                rpt = (now >= (expiries[3] + inteval));
                if (rpt) {
                    //m.mpLogger->mlog("locationRpt time interval, report 0x0200, update expiries[3] = %d", now);
                    logd("locationRpt time interval, report 0x0200, update expiries[3] = %d", now);

                    if (!st.getStateAcc()) { // acc off的上报
                        if (isSleepMode) {
                            //m.mpLogger->mlog("locationRpt sleep mode time interval %d, report 0200, update expiries[3] = %d", (!isSleepMOde ? timeout[3] : sleep_interval), now);
                            logd("locationRpt sleep mode time interval %d, report 0200, update expiries[3] = %d", (!isSleepMode ? timeout[3] : sleep_interval), now);
                            expiries[3] = now;
                        } else {
                            mAccOffRptCount++;
                            if (mAccOffRptCount >= accOffMaxCnt) { //此处是为了确保，acc off 后 30s时上报一次，60s时不上报
                                rpt = false;
                            } else {
                                //m.mpLogger->mlog("locationRpt acc off time interval %d, report 0200, update expiries[3] = %d", (!isSleepMOde ? timeout[3] : sleep_interval), now);
                                logd("locationRpt acc off time interval %d, report 0200, update expiries[3] = %d", (!isSleepMode ? timeout[3] : sleep_interval));
                                expiries[3] = now;
                            }
                        }
                    } else {  // acc on的上报
                        if (artificialAlarmTime) {
                             if (now > (artificialAlarmTime + 2)) {
                                 if (locRepTimeAdjust) {
                                     /* 因人工告警推迟第一次周期上报，缩短人工处理报警后第一次和第2次周期上报时间间隔，保证整体周期上报时间不会推迟 */
                                     expiries[3] = now - (now - (expiries[3] + (st.getSleepMode() ? timeout[3] : sleep_interval)));
                                     locRepTimeAdjust = 0;
                                     artificialAlarmTime = 0;
                                     m.mpLogger->mlog("locationRpt locRepTimeAdjust change time report!\n");
                                 } else {
                                     expiries[3] = now;
                                     artificialAlarmTime = 0;
                                 }
                             } else {
                                 m.mpLogger->mlog("locationRpt now:%d, artificialAlarmTime:%d!\n", now, artificialAlarmTime);
                                 logd("locationRpt now:%d, artificialAlarmTime:%d!\n", now, artificialAlarmTime);
                                 locRepTimeAdjust = 1;
                                 rpt = false;
                             }
                        } else {
                            expiries[3] = now;
                            //m.mpLogger->mlog("locationRpt acc on, time interval %d, report 0200, update expiries[3] = %d", (!isSleepMOde ? timeout[3] : sleep_interval), now);
                            logd("locationRpt acc on, time interval %d, report 0200, update expiries[3] = %d", (!isSleepMode ? timeout[3] : sleep_interval), now);
                        }
                        mAccOffRptCount = 0;
                    }
                }
            } else if (1 == config.sys.report.mode) { //定距上报
                if ((st.car.mileage * 1000 - mLastRptMileage * 1000) >= config.sys.report.default_distance) {
                    mLastRptMileage = st.car.mileage;
                    distanceRpt = true;
                    m.mpLogger->mlog("locationRpt, distance interval, report 0200, update expiries[3] = %d", now);
                    logd("locationRpt, distance interval, report 0200, update expiries[3] = %d", now);
                }
            } else if (2 == config.sys.report.mode) {
                if ((st.car.mileage * 1000 - mLastRptMileage * 1000) >= config.sys.report.default_distance) {
                    mLastRptMileage = st.car.mileage;
                    distanceRpt = true;

                    m.mpLogger->mlog("locationRpt, mdoe 2, report 0200, update expiries[3] = %d", now);
                    logd("locationRpt, mdoe 2, report 0200, update expiries[3] = %d", now);
                } else if (now >= (expiries[3] + (!isSleepMode ? timeout[3] : sleep_interval))) {
                    expiries[3] = now;
                    rpt = true;

                    m.mpLogger->mlog("locationRpt, mode 3, report 0200, update expiries[3] = %d", now);
                    logd("locationRpt, mode 3, report 0200, update expiries[3] = %d", now);
                }
            }
        }

        if (rpt || distanceRpt ||
            chkRptCond(ab, abLast, cb, sb, sbLast))
        {
            //除了定时定距 其它条件触发的0200，更新expiries[3]
            if (!rpt && !distanceRpt) {
                m.mpLogger->mlog("locationRpt other trigger, report 0200, update expiries[3] = %d", now);
                logd("locationRpt other trigger, report 0200, update expiries[3] = %d", now);
                expiries[3] = now;
            }

            if (!check_TPMS()) {
                int ret = this->report(st);
                if (!st.getStateAcc() && ((ab->emergTrig != abLast->emergTrig) && !cb->emergTrig) && !cb->emergTrig) {
                    /* acc off 状态下紧急报警仅发送一次 */
                    mbChkAlarmBits |= 1;
                    loge("clear cb->emergTrig:%d\n", mbChkAlarmBits);
                }
                LOG_IF(ret, logw, "[JttTcpClient:run] Failed to report schedule location, time=[%d], lat=[%3.3f], lng=[%3.3f]", now, st.lbs.lat, st.lbs.lng);
            }
        }

        if ((ab->pwrDWN != abLast->pwrDWN) && ab->pwrDWN) {
            m.ttsUtf8("主电源已断开", 10);
        } else if ((ab->pwrShort != abLast->pwrShort) && ab->pwrShort) {
            if (mCustom.bTTSPwrShort) {
                m.ttsUtf8("主电源欠压", 10);
            }
        }

        mRptState = (my::uint)st.getCurState();
        mRptAlarm = (my::uint)curAlarm;
    }
    if ((timeout[6] > 0) && (now >= expiries[6])) {
        heart_beat();
        expiries[6] = now + timeout[6];
    }
    return 0;
}

//检测预警、盲区补传、IC卡状态
int JttTcpClient::chkAlarmRpt()
{
    // 周期
    if ((my::uint)my::timestamp::now() >= expiries[4]) {
        expiries[4] = (my::uint)my::timestamp::now() + timeout[4];

        std::shared_ptr<minieye::AMessage> msg1 = std::make_shared<minieye::AMessage>(EVT_TYPE_PROT_DB_CHECK_AREA_ALARM, shared_from_this());
        msg1->post();

        std::shared_ptr<minieye::AMessage> msg2 = std::make_shared<minieye::AMessage>(EVT_TYPE_PROT_DB_CHECK_RPT_DATA, shared_from_this());
        msg2->post();
       // 检测ic卡状态，插入或者拔出需要触发X0702指令
        LOG_IF(check_iccard() != 0, logw, "[JttTcpClient:run] failed to check icccard.");
    }

    return 0;
}
    // 格式化当前时间
const char* HHMMSS_MSMS(char* buf)
{
    struct timeval tv;
    gettimeofday(&tv, NULL);
    struct tm      tmLocal;
    localtime_r(&tv.tv_sec, &tmLocal);

    snprintf(buf, 23, "%02d%02d%02d%04d",
        tmLocal.tm_hour,
        tmLocal.tm_min,
        tmLocal.tm_sec,
        (int)(tv.tv_usec/1000));
    return buf;
}

int JttTcpClient::canDataFeed(int canIdx, CanData & canData)
{
    Manager & m = Manager::getInstance();
    Config & config = m.config;
#if 1

    /* chuanbiao ext */
    if (extern_canDataDeal(canIdx, canData)){
        return 0;
    }

    //logd("rpt  can1UploadInterval=%f-%d ca2UploadInterval=%f-%d",
        //can1TS.elapsed(), config.sys.canParam.can1UploadInterval, can2TS.elapsed(), config.sys.canParam.can2UploadInterval);
    if (!config.sys.canParam.can1UploadInterval) {
        return 0;
    }
#endif
    //logd("can++ canIdx:%d, frameID:%d, data:%d", canIdx, canData.canID, canData.canData);
    if (0 == canIdx) {
        MY_SPINLOCK_X(lockcan1);
        if (0 == can1Data.size()) {
            char tmp[23];
            HHMMSS_MSMS(tmp);
            numstr2bcd(can1RecvTime, tmp, sizeof(can1RecvTime) * 2);
            logd("can++ updata can1collect time %02x-%02x-%02x-%02x-%02x", can1RecvTime[0], can1RecvTime[1], can1RecvTime[2], can1RecvTime[3], can1RecvTime[4]);
        }
        can1Data.push_back(canData);
        logd("can1Data size:%d!\n", can1Data.size());
    } else if (1 == canIdx) {
        MY_SPINLOCK_X(lockcan2);
        if (0 == can2Data.size()) {
            char tmp[23];
            HHMMSS_MSMS(tmp);
            numstr2bcd(can2RecvTime, tmp, sizeof(can2RecvTime) * 2);
            logd("can++ updata can2collect time %02x-%02x-%02x-%02x-%02x", can2RecvTime[0], can2RecvTime[1], can2RecvTime[2], can2RecvTime[3], can2RecvTime[4]);
        }
        can2Data.push_back(canData);
    }
    return 0;
}

int JttTcpClient::canDataRpt()
{
    Manager & m = Manager::getInstance();
    Config & config = m.config;
    //logd("rpt  can1UploadInterval=%d, can1 size=%d, ca2UploadInterval=%d, can2 size = %d",
         //config.sys.canParam.can1UploadInterval, can1Data.size(), config.sys.canParam.can2UploadInterval, can2Data.size());

    if (config.sys.canParam.can1UploadInterval != 0 && can1Data.size()) {
        int interval = (int)mCan1UploadTS.elapsed();

        if (interval  > (config.sys.canParam.can1UploadInterval* 1000)) {
            mCan1UploadTS = my::timestamp::now();
            logd("can++ enter uploadcan1 time%0x-%0x-%0x-%0x-%0x",
                can1RecvTime[0], can1RecvTime[1], can1RecvTime[2], can1RecvTime[3], can1RecvTime[4]);
            X0705 res;
            {
                MY_SPINLOCK_X(lockcan1);
                logd("can++ can1data size=%d", can1Data.size());
                for (vector<CanData>::iterator iter = can1Data.begin(); iter != can1Data.end(); ++iter) {
                    res.list.push_back(*iter);
                }
                memcpy(res.time, can1RecvTime, 5);
                can1Data.clear();
            }
            JttMsg msg = res.encode(sim, mProtVersion);
            if (!send(msg)) {
                loge("[X0705] failed to send  to server.");
            } else {
                logd("[X0705] success to send to server.");
            }
        }
    }

    if (config.sys.canParam.can2UploadInterval != 0 && can2Data.size()) {
        int interval = mCan2UploadTS.elapsed();
        if (interval > (config.sys.canParam.can2UploadInterval* 1000)) {
            mCan2UploadTS = my::timestamp::now();
            logd("can++ enter uploadcan2 time%0x-%0x-%0x-%0x-%0x",
                can2RecvTime[0], can2RecvTime[1], can2RecvTime[2], can2RecvTime[3], can2RecvTime[4]);
            X0705 res;
            {
                MY_SPINLOCK_X(lockcan2);
                logd("can++ can2data size=%d", can2Data.size());
                for (vector<CanData>::iterator iter = can2Data.begin(); iter != can2Data.end(); ++iter) {
                    res.list.push_back(*iter);
                }

                memcpy(res.time, can2RecvTime, 5);
                can2Data.clear();
            }
            JttMsg msg = res.encode(sim, mProtVersion);
            if (!send(msg)) {
                loge("[X0705] failed to send to server.");
            } else {
                loge("[X0705] success to send to server.");
            }
        }
    }

    return 0;
}

#define CHK_NEED_SLEEP(TS, duration) do { \
    Manager & m = Manager::getInstance(); \
    uint32_t d = runtimeTS.elapsed(); \
    if (d < duration) { \
        msleep(duration - d); \
    } else if (d >= 5000) {\
        m.mpLogger->mlog("%s : maybe block too long %d ms!", thrdName.c_str(), d);\
    } \
} while (0)
// 消息接收
void JttTcpClient::run()
{
    static time_t heartbeatSec = 0;
    std::string thrdName = prot_subtype.c_str();
    if (!thrdName.length()) {
        thrdName = tag;
    }
    prctl(PR_SET_NAME, thrdName.c_str());

    while (!exiting()) {
        my::timestamp runtimeTS = my::timestamp::now();
        Current st = ServiceHelper::getInstance().getStatus();

        time_t curTime = time(NULL);
        if (heartbeatSec != curTime) {
            heartbeatSec = curTime;
            char value[PROP_VALUE_MAX];
            memset(value, 0, sizeof(value));
            snprintf(value, sizeof(value), "%ld", heartbeatSec);
            __system_property_set(PROP_RW_MINIEYE_IDVR_CCU_HEARTBEAT, value);
        }

        //logd("[JttTcpClient::run] start, rpos=[%d], wpos=[%d], k=[%d]", rpos, wpos, k);
        // 处理超时请求或者响应
        msgRetry();

        // 检测超时疲劳驾驶
        check_tired_drv(st);

        ext_run_ahead();
        if (!chkOnline(st)) {
            CHK_NEED_SLEEP(runtimeTS, 1000);
            continue;
        }

        // 在TCP已连接的情况下, 允许收取数据
        // 虽然有可能还尚未注册或者授权, 但不影响设备可以被服务器反向控制
        int ret = 0;
        if ((0 > (ret = recv_parse())) ||
            (mLastMsgRcvTs.elapsed() >= (mProtRcvTimeoutSec * 1000))) {
            mLastMsgRcvTs = my::timestamp::now();
            mProtState = JttClient::INVALID;
            expiries[0] = 0;  /* TCP连接超时或者解析出错则尝试重连，通过将超时结束时间设置为0立刻重连 */
            clear();
            //loge("recv_parse ret = %d", ret);
            CHK_NEED_SLEEP(runtimeTS, 10);
            continue;
        }
        if (mbDummy) {
            logd("dummy");
            mProtState = JttClient::INVALID;
            expiries[0] = 0; // 立刻重连
            expiries[1] = 0; // 立刻重连
            resetAuthed(); // 立刻重连
            clear();
            CHK_NEED_SLEEP(runtimeTS, 1000);
            continue;
        }
        #if 1
        if (!chkSMS()) {
            continue;
        }
        #endif
        if (!chkRegStat()) {
            CHK_NEED_SLEEP(runtimeTS, 10);
            continue;
        }

        if (!chkAuthed()) {
            CHK_NEED_SLEEP(runtimeTS, 10);
            continue;
        }

        //位置上报
        locationRpt(st);

        //检测预警、盲区补传、IC卡状态
        chkAlarmRpt();

        canDataRpt();

        if (!ext_run()) {
            logw("ext_run return false!");
            CHK_NEED_SLEEP(runtimeTS, 10);
            continue;
        }

        clearStoppedFtpTsk();
        autoSnap();
        doSnapCmd();

        CHK_NEED_SLEEP(runtimeTS, 10);
    }
}

bool JttTcpClient::post(const my::constr& msg)
{
    int ret = 0, offset = 0, len = msg.length();
    const char * p = (const char*)msg;
    MY_SPINLOCK_X(lock);

    while (len > 0) {
        ret = c.send(p + offset, len);
        if (ret < 0) {
            if (errno == EAGAIN || errno == EWOULDBLOCK || errno == EINTR) {
                loge("try post msg again...\n");
                usleep(10e3);
            } else {
                loge("post msg fail! %s\n", strerror(errno));
                break;
            }
        } else {
            if (ret == 0) {
                setProtRcvTimeout(timeout[6] + 10); /* 消息发送成功后设置消息接收超时时间为30s */
                loge("post msg 0! %s\n", strerror(errno));
                usleep(10e3);
            }
            offset += ret;
            len    -= ret;
        }
    }

    // hexdump 时忽略附件内容
    if (!access("/data/dump_jtt808", R_OK) && (msg[0] == 0x7E)) {
            my::hexdump(msg, true);
    }

    Manager & m = Manager::getInstance();
    if (!access("/data/dump_jtt808_snd2file", R_OK) && m.mpLogger) {
        my::string hex = my::hex(msg, true);
        m.mpLogger->mlog("0x%02x%02x", msg[1], msg[2]);
        m.mpLogger->mlog_raw(hex.c_str(), hex.length());
    }

    return !len;
}

// 接收消息
bool JttTcpClient::get(my::string& msg)
{
    MY_SPINLOCK_X(lock);
    int ret = c.recv((char*)msg, msg.length());
    LOG_RETURN_IF(!ret || (ret < 0 && (EAGAIN != errno)), false, loge, "[JttTcpClient::get] failed to recv msg, ret=[%d]", ret);

    if (ret < 0 && (EAGAIN == errno)) {
        ::usleep(100000);
    }

    msg.length(ret > 0 ? ret : 0);
    return true;
}

bool JttTcpClient::tired_drv_rpt()
{
    Manager & m = Manager::getInstance();
    Current st = ServiceHelper::getInstance().getStatus();
    DmsAlarm alarm;

    m.current->setAlarmTired(true);
    m.current->setAlarmBeforeTired(false);
    alarm.event_type = DMS_WARN_TYPE_FATIGUE;
    alarm.ts_ms = my::timestamp::milliseconds_from_19700101();
    alarm.ts = (my::uint)(alarm.ts_ms / 1000);
    alarm.speed = (my::uchar)st.getSpeed();
    m.mpLogger->mlog("tired_drv_rpt ext fatigue !\n");
    alarm.evt_name = value2name("dms", EVT_TYPE_DMS_FATIGUE);

    char propValue[PROP_VALUE_MAX];
    snprintf(propValue, sizeof(propValue), "true");
    m.propertySet(PROP_PERSIST_MINIEYE_FATIGE_DRIVE, propValue);

    return dms_report(1, alarm) > 0;
}
bool JttTcpClient::tired_drv_tips(TripInfoT & trip, int timeLimit, bool black)
{
    bool rpt = false;
    Manager & m = Manager::getInstance();
    Current st = ServiceHelper::getInstance().getStatus();
    //Current::AlarmBits * pw = (Current::AlarmBits*)&m.current->alarm;
    conf_t & cfg = m.config.sys;
    time_t curSec = time(NULL);
    uint32_t tripTm = 0;
    if (trip.endTime > trip.startTime) {
        tripTm = trip.endTime - trip.startTime; 
    }
    int alarm_ahead_sec = (cfg.warn.tired_drv.alarm_ahead_minus * 60);

    if (timeLimit < alarm_ahead_sec) {
        alarm_ahead_sec = timeLimit / 4;
    }

    //logd("trip time %d, (%d ~ %d), timeLimit %d , cfg.warn.tired_drv.alarm_time_gap %d, curSec %d",
         //tripTm, trip.startTime, trip.endTime, timeLimit, cfg.warn.tired_drv.alarm_time_gap, curSec);
    if (((trip.endTime + cfg.warn.overtime.rest * 60) >= curSec) &&
        ((trip.startTime + timeLimit - alarm_ahead_sec) <= trip.endTime)) {
        if (trip.endTime < (trip.startTime + timeLimit)) {//alarm
            int * max_count = &mTiredDrv.stats[black].alarm_count;
            m.current->setAlarmTired(false);
            m.current->setAlarmBeforeTired(true);
            //pw->tiredDrv = 0;
            //pw->tiredDrvAlarm = 1;
            time_t * last_tm = &mTiredDrv.stats[black].last_alarm_tm;

            if (black) {
                max_count = &mTiredDrv.stats[black].alarm_count;
                last_tm = &mTiredDrv.stats[black].last_alarm_tm;
            }

            rpt = true;

            if ((*last_tm + cfg.warn.tired_drv.alarm_time_gap * 60) < curSec && st.getSpeed() >= 20) {
                if (*max_count < cfg.warn.tired_drv.alarm_tms) {
                    (*max_count)++;
                    if (tag == "conn1") {
                        string cmd = "cmd play /data/audios/VB.wav 99";
                        if (!LogCallProxyCmd::sendReq("media", cmd.c_str())) {
                            loge("sock cmd failed %s", cmd.c_str());
                        }
                        char spch[128] = {0};
                        snprintf(spch, sizeof(spch), "您已驾驶%d小时%d分", tripTm / 3600, tripTm % 3600 / 60);
                        ttsPlay(false, 99, "%s", spch);
                        m.ttsGBK(cfg.warn.tired_drv.alarm_spch);
                        m.mpLogger->mlog("ext fatigue alarm! trip %d timeLimit %d", tripTm, timeLimit);
                    }
                } else {
                    *last_tm = curSec + timeLimit * !cfg.warn.tired_drv.alarm_time_gap;
                    (*max_count) = 0;
                }

                logd("trip time %d, (%d ~ %d), timeLimit %d, max_count %d, last_tm %d, cfg.warn.tired_drv.alarm_time_gap %d, curSec %d",
                     tripTm, trip.startTime, trip.endTime, timeLimit, *max_count, *last_tm, cfg.warn.tired_drv.alarm_time_gap, curSec);

            } else {
                mTiredDrv.stats[black].warn_count = 0;
            }

        } else {//warn
            int * max_count = &mTiredDrv.stats[black].warn_count;
            time_t * last_tm = &mTiredDrv.stats[black].last_warn_tm;

            if (black) {
                max_count = &mTiredDrv.stats[black].warn_count;
                last_tm = &mTiredDrv.stats[black].last_warn_tm;
            }

            rpt = true;

            if ((*last_tm + cfg.warn.tired_drv.warn_time_gap * 60) < curSec && st.getSpeed() >= 20) {
                if (*max_count < cfg.warn.tired_drv.warn_tms) {
                    if (!(*max_count)) {
                        tired_drv_rpt();
                        m.mpLogger->mlog("ext fatigue occur! trip %d timeLimit %d", tripTm, timeLimit);
                    }

                    (*max_count)++;
                    if (tag == "conn1") {
                        string cmd = "cmd play /data/audios/FCW.wav 99";
                        if (!LogCallProxyCmd::sendReq("media", cmd.c_str())) {
                            loge("sock cmd failed %s", cmd.c_str());
                        }
                        char spch[128] = {0};
                        snprintf(spch, sizeof(spch), "您已驾驶%d小时%d分", tripTm / 3600, tripTm % 3600 / 60);
                        ttsPlay(false, 99, "%s", spch);
                        m.ttsGBK(cfg.warn.tired_drv.warn_spch);
                    }
                } else {
                    (*max_count) = 0;
                    *last_tm = curSec;
                }

            } else {
                mTiredDrv.stats[black].alarm_count = 0;
            }
        }

    } else {
        m.current->setAlarmTired(false);
        m.current->setAlarmBeforeTired(false);
        //pw->tiredDrv = 0;
        //pw->tiredDrvAlarm = 0;
        mTiredDrv.stats[black].alarm_count = 0;
        mTiredDrv.stats[black].warn_count = 0;
    }

    return rpt;
}

bool JttTcpClient::check_tired_drv(Current st)
{
    Manager & m = Manager::getInstance();
    conf_t & cfg = m.config.sys;
    TripInfoT trip;
    bool ret = st.get_trip(trip);
    if (ret == false) {
        return false;
    }

    int drvTmLimit = cfg.warn.overtime.limit * 360 + cfg.warn.overtime.remainder;

    if (!access("/data/test", R_OK)) {
        if ((cfg.warn.overtime.oneDayLimit > 0) && (st.getSpeed() >= 10.0) &&
            (trip.todayTotal >= cfg.warn.overtime.oneDayLimit)) {
            m.current->setAlarmDriveOvertime(true);
            //pw->drvTmOvflw = 1;
            if (mLastTtsTm.elapsed() > 5000) {
                mLastTtsTm = my::timestamp::now();
                if (tag == "conn1") {
                    ttsPlay(false, 5, "累计驾驶超时");
                    //logd("累计驾驶超时");
                }
            }
        } else {
            m.current->setAlarmDriveOvertime(false);
            //pw->drvTmOvflw = 0;
        }
    }

    if (!drvTmLimit) {
        //logd("Disable tired drv as cfg.overspeed.max_drv_tm_thres == 0");
        //Current::AlarmBits * pw = (Current::AlarmBits*)&m.current->alarm;
        //pw->tiredDrv = 0;
        m.current->setAlarmTired(false);
        return false;
    }

    bool enable = cfg.warn.tired_drv.drv_tm_max_sec && (cfg.warn.tired_drv.datetime_bits & 4) && (cfg.warn.tired_drv.time_bgn) && (cfg.warn.tired_drv.time_end);
    //logd("+check_tired_drv %d, %d-%d", cfg.warn.tired_drv.datetime_bits, (cfg.warn.tired_drv.time_bgn), (cfg.warn.tired_drv.time_end));
    bool rpt = false;

    if (enable) {
        bool isNightTm = inDayTmSeg(cfg.warn.tired_drv.time_bgn, cfg.warn.tired_drv.time_end);

        if (isNightTm) {
            int32_t diffBgn = 0;
            bool isTripNightBgn = inDayTmSeg(cfg.warn.tired_drv.time_bgn, cfg.warn.tired_drv.time_end, &trip.startTime, &diffBgn);
            TripInfoT nightTrip = trip;
            if (!isTripNightBgn) {
                nightTrip.startTime -= diffBgn;
            }

            rpt = tired_drv_tips(nightTrip, cfg.warn.tired_drv.drv_tm_max_sec, true);
            if (!access("/mnt/obb/mprot/trace_tired", R_OK)) {
                logd("isNightTm %d, night [%u-%u] = %u, total [%u-%u] = %u,", isNightTm,
                     nightTrip.endTime, nightTrip.startTime,
                     nightTrip.endTime - nightTrip.startTime,
                     trip.endTime, trip.startTime,
                     trip.endTime - trip.startTime);
            }
        }
    }

    if (!rpt) {
        tired_drv_tips(trip, drvTmLimit);
    }

    if ((trip.startTime == trip.endTime) && (st.getStateAcc())) {
        char propValue[PROP_VALUE_MAX] = {0};
        __system_property_get(PROP_PERSIST_MINIEYE_FATIGE_DRIVE, propValue);

        if (strcmp(propValue, "false") && (tag == "conn1")) {
            if (mTiredDrv.restSpch_count++ < 3) {
                m.ttsUtf8("您已解除疲劳驾驶");
            }

            if (3 == mTiredDrv.restSpch_count) {
                sprintf(propValue, "false");
                m.propertySet(PROP_PERSIST_MINIEYE_FATIGE_DRIVE, propValue);
            }
        }

    } else {
        mTiredDrv.restSpch_count = 0;
    }

    //logd("-check_tired_drv");
    return true;
}


void JttAtCp2Disk::run()
{
    prctl(PR_SET_NAME, "alarmAttCopy");

    while (!exiting()) {
        {
            MY_SPINLOCK_X(mCpLock);
            auto it2cp = mAtPath2CpList.begin();

            while (it2cp != mAtPath2CpList.end()) {
                my::uint64 cur = my::timestamp::milliseconds_from_19700101();
                my::uint64 diffSec = ((cur - it2cp->second)) / 1000;
                //logd("start cp : %s, diffSec:%lld!\n", it2cp->first.c_str(), diffSec);
                if (diffSec > 10) {
                    if (!access(it2cp->first.c_str(), R_OK)) {
                        std::string path = it2cp->first.c_str();
                        //logd("cPpath= %s", path.c_str());
                        char * p = strrchr((char*)path.c_str(), '/');
                        p[0] = 0;
                        p = strrchr((char*)path.c_str(), '/');
                        string folder = p;
                        string targetFolder = "/mnt/media_rw/$mnt/MNEYE/";
                        struct tm dt;
                        time_t s = time(NULL);
                        localtime_r(&s, &dt);
                        char date[256];
                        snprintf(date, sizeof(date), "%04d%02d%02d/alarm/",
                                 dt.tm_year + 1900, dt.tm_mon + 1, dt.tm_mday);
                        targetFolder += date;
                        targetFolder += folder;
                        logd("targetFolder : %s!\n", targetFolder.c_str());
                        std::string cmd = "mnt=`grep /disk1 /proc/mounts`; if [ -z \"$mnt\" ] ; then mnt=`grep /sdcard1 /proc/mounts` && [ ! -z \"$mnt\" ] && mnt=sdcard1; else mnt=disk1; fi ; [ ! -z \"$mnt\" ] && mkdir -p ";
                        cmd += targetFolder;
                        cmd += "; [ ! -z \"$mnt\" ] && cp ";
                        cmd += it2cp->first.c_str();
                        cmd += " ";
                        cmd += targetFolder;
                        logd("cp to sdcard cmd : %s!\n", cmd.c_str());
                        system(cmd.c_str());
                        it2cp = mAtPath2CpList.erase(it2cp);
                        break;/*break to avoid lock blocking*/
                    }
                }

                if (diffSec > 600) {
                    logd("timeout : %ld, %s", diffSec, it2cp->first.c_str());
                    it2cp = mAtPath2CpList.erase(it2cp);
                    continue;
                }

                it2cp++;
            }
        }

        {
            MY_SPINLOCK_X(mClearLock);
            auto it = mAtFolderList.begin();

            while (it != mAtFolderList.end()) {
                my::uint64 cur = my::timestamp::milliseconds_from_19700101();
                my::uint64 diffSec = ((cur - it->second)) / 1000;

                if (diffSec > AT_TASK_TIMEOUT_SEC) {
                    string cmd = "rm -r ";
                    string folder = it->first;
                    cmd += folder.c_str();
                    system(cmd.c_str());
                    it = mAtFolderList.erase(it);

                } else {
                    it++;
                }
            }
        }
        my::thread::usleep(200 * 1000);
    }
}


