#ifndef __IDVRCORE_PROT_JTT808_1078_H__
#define __IDVRCORE_PROT_JTT808_1078_H__
#include <sys/prctl.h>
#include <vector>
#include <sys/types.h>
#include <dirent.h>
#include "ALooper.h"
#include "AMessage.h"
#include "AHandler.h"

#include "mystd.h"
#include "service.h"
#include "prot.jtt808-base.h"
#include "current.h"
#include "service.helper.h"
#include "manager.h"
#include "idvrProperty.h"
#include "prot.jtt808-1078.ftp.h"


class Resolution
{
    public:
        static int getWidth(const int type)
        {
            static int resolution[][2] = {
                { 320, 240 }, { 640, 480 }, { 800, 600 }, { 1024, 768 },
                { 176, 144 }, { 352, 288 }, { 704, 288 }, { 704, 576 }, {1280, 720}
            };
            int len = sizeof(resolution) / sizeof(resolution[0]);
            int pos = (type < (len - 1) && type >= 0) ? type : (len - 1);
            return resolution[pos][0];
        }

        static int getHeight(const int type)
        {
            static int resolution[][2] = {
                { 320, 240 }, { 640, 480 }, { 800, 600 }, { 1024, 768 },
                { 176, 144 }, { 352, 288 }, { 704, 288 }, { 704, 576 }, {1280, 720}
            };
            int len = sizeof(resolution) / sizeof(resolution[0]);
            int pos = (type < (len - 1) && type >= 0) ? type : (len - 1);
            return resolution[pos][1];
        }
};
#define BSP_NAME "bsp-dfh2"
string getSwVer(const char * swName = BSP_NAME);

// JTT部标客户端, 根据协议有3种：基于短消息, 基于TCP, 基于UDP
class JttClient
    : public JttProtBase
    , public minieye::AHandler
{
        friend class JttMsgHandler;
        friend class JttFtpUpload;
    public:
        enum {
            INVALID         = 0x0,
            ONLINE          = 0x1,    // 在线状态
            JttREGISTERED   = 0x2,    // 已注册
            AUTHORIZED      = 0x4,    // 已（登录）授权
        };
        my::string prot_subtype = "";
    public:
        virtual int dmsEvt2protEvt(EVT_TYPE evt)
        {
            static const std::map<EVT_TYPE, DMS_WARN_TYPE_E> dmsEWMap = {
                {
                    {EVT_TYPE_DMS_FATIGUE,      DMS_WARN_TYPE_FATIGUE},
                    {EVT_TYPE_DMS_FATIGUE_Eye,  DMS_WARN_TYPE_FATIGUE},
                    {EVT_TYPE_DMS_FATIGUE_YAWN, DMS_WARN_TYPE_FATIGUE},

                    {EVT_TYPE_DMS_LOOK_AROUND,  DMS_WARN_TYPE_DISTRACT},
                    {EVT_TYPE_DMS_LOOK_DOWN,    DMS_WARN_TYPE_DISTRACT},
                    {EVT_TYPE_DMS_LOOK_UP,      DMS_WARN_TYPE_DISTRACT},
                    {EVT_TYPE_DMS_PHONE_CALL,   DMS_WARN_TYPE_CALLING},
                    {EVT_TYPE_DMS_SMOKE,        DMS_WARN_TYPE_SMOKING},
                    {EVT_TYPE_DMS_ABSENCE,      DMS_WARN_TYPE_ABNORMAL},
                    {EVT_TYPE_DMS_SNAP,         DMS_WARN_TYPE_SNAPSHOT_EVENT},
                    {EVT_TYPE_DMS_DRIVER_CHG,   DMS_WARN_TYPE_DRIVER_CHANGE},
                },
            };

            auto p = dmsEWMap.find(evt);

            if (p != dmsEWMap.end()) {
                return p->second;

            }

            return DMS_WARN_TYPE_INVALID;
        }
        virtual int hodEvt2protEvt(EVT_TYPE evt)
        {
            return DMS_WARN_TYPE_INVALID;
        }
        virtual int adasEvt2protEvt(EVT_TYPE evt)
        {
            static const std::map<EVT_TYPE, ADAS_WARN_TYPE_E> adasEWMap = {
                {
                    {EVT_TYPE_ADAS_FCW,         ADAS_WARN_TYPE_FCW},
                    {EVT_TYPE_ADAS_LeftLDW,     ADAS_WARN_TYPE_LDW},
                    {EVT_TYPE_ADAS_RightLDW,    ADAS_WARN_TYPE_LDW},
                    {EVT_TYPE_ADAS_HW,          ADAS_WARN_TYPE_HW},
                    {EVT_TYPE_ADAS_PCW,         ADAS_WARN_TYPE_PCW},
                    {EVT_TYPE_ADAS_TSRW,        ADAS_WARN_TYPE_TSRW},
                    {EVT_TYPE_ADAS_TSR,         ADAS_WARN_TYPE_TSR},
                    {EVT_TYPE_ADAS_SNAP,        ADAS_WARN_TYPE_SNAP},
                },
            };
            auto p = adasEWMap.find(evt);

            if (p != adasEWMap.end()) {
                return p->second;

            }
            return ADAS_WARN_TYPE_INVALID;
        }

        virtual int bsdEvt2protEvt(EVT_TYPE evt, const char * evtName)
        {
            std::map<EVT_TYPE, BSD_WARN_TYPE_E> dmsEWMap = {
                {EVT_TYPE_BSD_Behind,   BSD_WARN_TYPE_BEHIND},
                {EVT_TYPE_ABSD_Behind,  BSD_WARN_TYPE_BEHIND},
                {EVT_TYPE_BSD_Left,     BSD_WARN_TYPE_LEFT},
                {EVT_TYPE_ABSD_Left,    BSD_WARN_TYPE_LEFT},
                {EVT_TYPE_BSD_Right,    BSD_WARN_TYPE_RIGHT},
                {EVT_TYPE_ABSD_Right,   BSD_WARN_TYPE_RIGHT},
                {EVT_TYPE_BSD_Front,    BSD_WARN_TYPE_FRONT},
                {EVT_TYPE_ABSD_Front,   BSD_WARN_TYPE_FRONT},
            };

            if (evtName) {//自定义bsd值
                char propValue[PROP_VALUE_MAX] = {0};
                memset(propValue, 0, sizeof(propValue));
                char propName[128];
                snprintf(propName, sizeof(propName), PROP_PERSIST_MINIEYE_JTT808_EVT_VAL_FMT, evtName);
                if (__system_property_get(propName, propValue) > 0) {
                    int val = atoi(propValue);
                    if (val >= 0) {
                        return (BSD_WARN_TYPE_E)val;
                    }
                }
            }
            auto p = dmsEWMap.find(evt);

            if (p != dmsEWMap.end()) {
                return p->second;

            } else {
                logd("not find BSD evt %d\n", evt);
            }
            return BSD_WARN_TYPE_INVALID;
        }
        virtual int vmsEvt2protEvt(EVT_TYPE evt)
        {
            static const std::map<EVT_TYPE, VMS_WARN_TYPE_E> vmsEWMap = {

            };

            auto p = vmsEWMap.find(evt);

            if (p != vmsEWMap.end()) {
                return p->second;

            }
            return VMS_WARN_TYPE_INVALID;
        }

        virtual void displayWarnBmp(int level, int duration = 10)
        {
            const char * bmp[] = {"/system/etc/img/red.bmp", "/system/etc/img/yellow.bmp", "/system/etc/img/green.bmp"};
            const char * pBmp = bmp[0];
            if ((level > 0) && (level <= sizeof(bmp) / sizeof(bmp[0]))) {
                pBmp = bmp[level - 1];
            }
            int scale = 100;
            char value[PROP_VALUE_MAX] = {0};
            if (__system_property_get(PROP_RW_MINIEYE_BSD_WARN_BMP_SCALE, value) > 0) {
                scale = atoi(value);
            }

            char cmd[256] = {0};
            snprintf(cmd, sizeof(cmd), "cmd alarm 1 %s %d 1 %d", pBmp, duration, scale);
            vector<char> resp;
            LogCallProxyCmd::sendReq("media", cmd, resp, 1);
            return;
        }

    public:
        JttClient(const char * pSim) : mode(0), mProtState(JttClient::INVALID)
        {
            Manager &m = Manager::getInstance();
            Config & config = m.config;
            tcp_retry_max = config.sys.report.tcp_retry_max;
            timeout[0] = config.sys.report.tcp_timeout; // 当TCP断开连接时, 每隔3秒钟重连一次
            timeout[1] = 5; // 当注册失败时, 每隔5秒钟重试一次
            timeout[2] = 20; // 当鉴权失败时, 每隔20秒钟重试一次
            timeout[3] = config.sys.report.default_inteval; // 每隔5秒钟上报gps
            timeout[3] += !timeout[3];/*不能为0*/
            timeout[4] = 1; // 每隔1秒钟检测预警
            timeout[5] = 0; // 每隔几秒临时上报gps, 0为不上报
            timeout[6] = config.sys.report.hearbeat_inteval;
            timeout[7] = config.sys.gnss.detail_upint;

            expiries[0] = 0; // 初始时, 立刻链路连接
            expiries[1] = 0; // 初始时, 立刻注册
            expiries[2] = 0; // 初始时, 立刻鉴权
            expiries[3] = 0; // 初始时, 立刻上报gps
            expiries[4] = 0; // 初始时, 立刻检测预警
            expiries[5] = 0; // 临时跟踪过期时间, 0为不上报
            expiries[6] = 0; // 初始时，立刻心跳上报
            expiries[7] = 0; // 上报GNSS数据


            trace_expire = 0; // 临时跟踪过期
            com_service = NULL;
            is_main = false;

            sim = pSim; // sim卡信息
            mAlarmLvlSpdThrs = 50;

            char propValue[PROP_VALUE_MAX] = {0};
            memset(propValue, 0, sizeof(propValue));

            if (__system_property_get(PROP_PERSIST_MINIEYE_JTT808RPTLVLSPD, propValue) > 0) {
                int val = atoi(propValue);

                if (val >= 0) {
                    mAlarmLvlSpdThrs = val;
                }
            }

            if (__system_property_get(PROP_PERSIST_MINIEYE_JTT808_NOMSG_TIMEOUT, propValue) > 0) {
                int val = atoi(propValue);

                if (val >= 0) {
                    mProtRcvTimeoutSec = val;
                }
            }
            mLastMsgRcvTs = my::timestamp::now();

            logd("mAlarmLvlSpdThrs = %d", mAlarmLvlSpdThrs);
            mRptState = 0;
            mRptAlarm = 0;
            if (config.sys.videoAlarmMask.val == 127) {
                mCustom.b0200NoAlarmAtt = true;
            } else {
                mCustom.b0200NoAlarmAtt = false;
            }

            memset(propValue, 0, sizeof(propValue));
            if (__system_property_get(PROP_PERSIST_JTT808_TTS_PWR_SHORT, propValue) > 0) {
                if (!strcmp(propValue, "true")) {
                    mCustom.bTTSPwrShort = true;
                } else {
                    mCustom.bTTSPwrShort = false;
                }
            }
            #if 0
            memset(propValue, 0, sizeof(propValue));
            if (__system_property_get(PROP_PERSIST_JTT808_ALARM_MASK, propValue) > 0) {
                mCustom.alarmMask = (uint32_t)atoi(propValue);
            }
            #else
            mCustom.alarmMask = config.sys.alarmMask.val;
            #endif
            mCustom.videoAlarmMask = config.sys.videoAlarmMask.val;
            mbNeedAuth = !!access("/data/NoAuth", R_OK);
            memset(&mSnapCmd, 0, sizeof(mSnapCmd));

            mAMsgLooper = std::make_shared<minieye::ALooper>();
            setLooper(mAMsgLooper);
            mAMsgLooper->setName("msgLooper");
            mAMsgLooper->start();
            mOnlineTs = my::timestamp::now();
            mCan1UploadTS = my::timestamp::now();
            mCan2UploadTS = my::timestamp::now();
            mAccOffTS = my::timestamp::now();
            mAccOffRptCount = 0;
        }

        virtual ~JttClient() {}
    private:
        std::shared_ptr<minieye::ALooper> mAMsgLooper;
        void onMessageReceived(const std::shared_ptr<minieye::AMessage> &msg);
    public:
        virtual void refreshAlarmMask(my::uint val) {
            mCustom.alarmMask = val;
        }
        virtual void refreshVideoAlarmMask(my::uint val) {
            mCustom.videoAlarmMask = val;
            if (mCustom.b0200NoAlarmAtt) {
                if (val != 127) {
                    mCustom.b0200NoAlarmAtt = false;
                }
            } else {
                if (val == 127) {
                    mCustom.b0200NoAlarmAtt = true;
                }
            }
        }

        /*获取算法事件对应的附件通道列表*/
        virtual void get_attachCh(const char *algName, std::vector<int> & chVector);

        virtual int attSndLvl()
        {
            return 2;
        }
        int get_prot_version() {return mProtVersion;};
        void dummy(bool enable){mbDummy = enable;}
        virtual bool ext_proc(const my::string& sim, my::ushort cmd, my::ushort sn, const my::constr& data) = 0;

        virtual bool extProcArea(const char* audio)
        {
            return true;
        }
        virtual bool extProcWayBill(const char* audio)
        {
            return true;
        }
        virtual bool ext_run() = 0;
        virtual bool ext_x0200_addition_info(const Current& st, X0200 & req) = 0;

        //协议子类Amessage处理函数, 注意返回值
        virtual bool onMsgRcved(const std::shared_ptr<minieye::AMessage> &msg)
        {
            return false;
        }
        virtual void add2cp(std::string path) {};
        // 检查应答
        int check_res(bool jt808_new);

        // 获取9212
        int check_9212(bool jt808_new, X9212 & rsp);

        bool is_main_server()
        {
            return is_main;
        }

        void set_main_server()
        {
            is_main = true;
        }

    public:
        bool  getJtt808ProtVer()
        {
            return (com_service->si.attSndProtVer < 0) ? mProtVersion : com_service->si.attSndProtVer;
        }

        // 获取心跳间隔，单位秒
        void set_heartbeat_inteval(my::uint val)
        {
            timeout[6] = val;
        }
        my::uint get_heartbeat_inteval() const
        {
            return timeout[6];
        }


        // TCP应答超时
        void set_tcp_timeout(my::uint val)
        {
            timeout[0] = val;
        }
        my::uint get_tcp_timeout() const
        {
            return timeout[0];
        }

        // tcp重传次数
        void set_tcp_retry(my::uint val)
        {
            tcp_retry_max = val;
        }
        my::uint get_tcp_retry() const
        {
            return tcp_retry_max;
        }

        void set_gps_timeout(my::uint val)
        {
            timeout[3] = val;
        }
        my::uint get_gps_timeout()
        {
            return timeout[3];
        }
        void set_gnss_timeout(my::uint val)
        {
            timeout[7] = val;
        }
        my::uint get_gnss_timeout()
        {
            return timeout[7];
        }
        virtual void set_ctrl_speed(uint8_t speed)
        {
        }
        virtual void set_lock_car(bool flag)
        {
        }
        virtual void set_limit_lift(bool flag)
        {
        }
    public:

        virtual void send_attachment(AlarmInfoItem & ai) = 0;

        // 注销
        int deregister();
        int queryServerTime();
        /* chuangbiao ext */
        int responseDriverInfo();
    protected:

        // 连接
        virtual int connect()
        {
            return 0;
        }

        // 注册
        int enregister();


        // 鉴权/登录
        int authroize();

        virtual bool addRcvCmdFilter(std::map<my::ushort, my::ushort> & cmdFilter) {

            if (cmdFilter.size()) {
                for (auto it = cmdFilter.begin(); it != cmdFilter.end(); it++) {
                    if (mRcvCmdFilter.find(it->first) == mRcvCmdFilter.end()) {
                        mRcvCmdFilter[it->first] = it->second;
                    }
                }
            }
            return true;
        }
        virtual void clearRcvCmdFilter() {
            mRcvCmdFilter.clear();
        }
        virtual bool checkRcvCmdFilter(my::ushort cmd) {
            if (!mRcvCmdFilter.size() ||
                (mRcvCmdFilter.find(cmd) != mRcvCmdFilter.end())) {
                return true;
            }
            return false;
        }
        // 处理服务器的响应或者请求消息
        virtual void onProtMsg(const my::string& sim, my::ushort cmd, my::ushort sn, const my::constr& data);
        // 通用应答
        virtual bool reply(const my::string& sim, my::ushort cmd, my::ushort sn, char res);

    private:
        int read(my::string& msg);
    protected:
        virtual bool ext_procX8001(X8001 & rsp)
        {
            return false;
        }
        bool proc8001(const my::string& sim, my::ushort sn, const my::constr& data); // 通用应答
        bool proc8003(const my::string& sim, my::ushort sn, const my::constr& data); // 重传请求
        bool proc8100(const my::string& sim, my::ushort sn, const my::constr& data); // 注册应答

        virtual bool proc8103(const my::string& sim, my::ushort sn, const my::constr& data); // 设置终端参数
        bool proc8104(const my::string& sim, my::ushort sn, const my::constr& data); // 查询终端参数
        virtual bool ext_procX8105(X8105 & req)
        {
            return false;
        }
        bool proc8105(const my::string& sim, my::ushort sn, const my::constr& data); // 控制指令
        virtual bool proc8106(const my::string& sim, my::ushort sn, const my::constr& data); // 查询终端参数
        virtual bool proc8107(const my::string& sim, my::ushort sn, const my::constr& data); // 查询终端版本
        bool proc8108(const my::string& sim, my::ushort sn, const my::constr& data); // 升级

        bool proc8201(const my::string& sim, my::ushort sn, const my::constr& data); // 位置信息查询
        bool proc8202(const my::string& sim, my::ushort sn, const my::constr& data); // 临时位置跟踪控制
        bool proc8203(const my::string& sim, my::ushort sn, const my::constr& data); // 清除告警标志

        bool proc8300(const my::string& sim, my::ushort sn, const my::constr& data); // 文本信息下发
        bool proc8302(const my::string& sim, my::ushort sn, const my::constr& data); // 提问下发
        bool proc8303(const my::string& sim, my::ushort sn, const my::constr& data); // 信息点播菜单设置
        bool proc8304(const my::string& sim, my::ushort sn, const my::constr& data); // 信息服务
        bool proc8401(const my::string& sim, my::ushort sn, const my::constr& data); // 设置电话本
        bool proc8500(const my::string& sim, my::ushort sn, const my::constr& data); // 车辆控制

        bool proc8600(const my::string& sim, my::ushort sn, const my::constr& data); // 设置圆形区域
        bool proc8601(const my::string& sim, my::ushort sn, const my::constr& data); // 删除圆形区域
        bool proc8602(const my::string& sim, my::ushort sn, const my::constr& data); // 设置矩形区域
        bool proc8603(const my::string& sim, my::ushort sn, const my::constr& data); // 删除矩形区域
        bool proc8604(const my::string& sim, my::ushort sn, const my::constr& data); // 设置多边形区域
        bool proc8605(const my::string& sim, my::ushort sn, const my::constr& data); // 删除多边形区域
        bool proc8606(const my::string& sim, my::ushort sn, const my::constr& data); // 设置路线
        bool proc8607(const my::string& sim, my::ushort sn, const my::constr& data); // 删除路线
        virtual bool proc8608(const my::string& sim, my::ushort sn, const my::constr& data); // 查询区域或线路

        bool proc8701(const my::string& sim, my::ushort sn, const my::constr& data);
        bool proc8702(const my::string& sim, my::ushort sn, const my::constr& data); // 上传驾驶员身份信息请求

        bool proc8800(const my::string& sim, my::ushort sn, const my::constr& data); // 多媒体数据上传应答
        bool sendMedia(const my::string& sim, my::uint64 timeMs, std::string fileName, int ch);
        void autoSnap();
        void doSnapCmd();
        virtual bool proc8801(const my::string& sim, my::ushort sn, const my::constr& data); // 抓取图片
        bool proc8802(const my::string& sim, my::ushort sn, const my::constr& data); // 存储多媒体数据检索
        bool proc8803(const my::string& sim, my::ushort sn, const my::constr& data); // 存储多媒体数据上传命令
        bool proc8804(const my::string& sim, my::ushort sn, const my::constr& data);
        bool proc8805(const my::string& sim, my::ushort sn, const my::constr& data); // 单条存储多媒体数据检索上传命令
        bool proc8900(const my::string& sim, my::ushort sn, const my::constr& data);
        virtual bool proc8900_ext(const my::string& sim, my::ushort sn, X8900 & req){
            return false;
        }
        bool proc9003(const my::string& sim, my::ushort sn, const my::constr& data); // 音视频属性

        virtual bool proc9101(const my::string& sim, my::ushort sn, const my::constr& data); // 播放实时音视频
        bool proc9102(const my::string& sim, my::ushort sn, const my::constr& data); // 音视频实时传输控制
        bool proc9105(const my::string& sim, my::ushort sn, const my::constr& data); // 实时音视频传输状态通知

        virtual bool proc9201(const my::string& sim, my::ushort sn, const my::constr& data); // 平台下发远程录像回放请求
        bool proc9202(const my::string& sim, my::ushort sn, const my::constr& data); // 平台下发远程录像回放控制
        int32_t SOSDataQuery(int ch, int code, uint32_t begin, uint32_t end, std :: vector < NewMedia > & list);
        int32_t recordQuery(int ch, int code, uint32_t begin, uint32_t end,
                                    std :: vector < NewMedia > & list, my::uint64 alarmTg = 0, my::uchar mediaType = 0);
        bool proc9205(const my::string& sim, my::ushort sn, const my::constr& data); // 查询资源列表
        virtual bool proc9206(const my::string& sim, my::ushort sn, const my::constr& data); // 文件上传指令
        bool proc9207(const my::string& sim, my::ushort sn, const my::constr& data);
        bool proc9208(const my::string& sim, my::ushort sn, const my::constr& data); // 报警附件上传指令
        bool proc9212(const my::string& sim, my::ushort sn, const my::constr& data); // 文件上传完成消息应答
        bool fake8700(X8700 req, const my::constr data, const my::string& sim, my::ushort sn);//for test
        bool proc8700(const my::string& sim, my::ushort sn, const my::constr& data);
    public:
        // 判断是否已连接上
        inline bool connected()
        {
            return (mProtState & (ONLINE | AUTHORIZED)) == (ONLINE | AUTHORIZED);
        }

        // 心跳消息
        int heart_beat();

        // 位置/状态/告警上报
        virtual int report(const Current& st, X0200 & req);
        virtual int report(const Current& st);
        virtual int save(const Current& st);
        // GNSS详细数据上报
        virtual int gnssRpt() = 0;
        int report(X0200& msg);

        bool check_TPMS();

        // 检查预警信息
        virtual int check_alarm(bool rpt = true);

        // 检查待上报信息
        int check_report_data();

        // 检查IC卡状态
        virtual int check_iccard();

        //检测报警对应的视频前后时间
        virtual void check_bgn_end_time(int alarm_type, time_t cur, time_t &bgn, time_t &end);

        //清理已完成的ftp任务
        void clearStoppedFtpTsk();

        // 生成告警附件
        virtual int creat_adas_accessory(int ch, AdasAlarm  * adas, AlarmInfoItem *alarm_info, my::string *path);
        virtual int creat_dms_accessory(int ch, DmsAlarm      * dms, AlarmInfoItem *alarm_info, my::string *path);
        virtual int creat_hod_accessory(int ch, DmsAlarm *dms, AlarmInfoItem *alarm_info, my::string *path);
        virtual int creat_speeding_accessory(int ch, speedingAlarm *speeding, AlarmInfoItem *alarm_info, my::string *path);
        virtual int creat_over_height_accessory(int ch, overHeightAlarm *overHeight, AlarmInfoItem *alarm_info, my::string *path);
        virtual int creat_over_load_accessory(int ch, overLoadAlarm *overLoad, AlarmInfoItem *alarm_info, my::string *path);
        // 生成告警等级
        virtual int get_adas_alarm_level(my::uchar speed, my::uchar evt, my::uint64 ts_ms){
            return (speed > mAlarmLvlSpdThrs) ? 2 : 1;
        }
        virtual int get_dms_alarm_level(my::uchar speed, my::uchar evt, my::uint64 ts_ms){
            return (speed > mAlarmLvlSpdThrs) ? 2 : 1;
        }

        int tpms_report(int ch, TpmsAlarm & tpms);

        // bsd 上报接口
        virtual int bsd_report(int ch, BsdAlarm & bsd);

        // dms上报接口
        virtual int dms_report(int ch, DmsAlarm& adas);

        // hod上报接口
        int hod_report(int ch, DmsAlarm& adas);

        // adas上报接口
        virtual int adas_report(int ch, AdasAlarm& adas);

        // vms上报接口(车辆监测系统)
        virtual int vms_report(int ch, VmsAlarm& vms);

        /* 超速上报接口 川标  extern       */
        int speeding_report(int ch, speedingAlarm& speeding);

        /* 超过道路限高告警          川标 extern */
        int over_height_report(int ch, overHeightAlarm& overHeight);
        /* 超过道路限重告警          川标 extern */
        int over_load_report(int ch, overLoadAlarm& overLoad);

        //渣土车上报接口
        virtual int ZTC_report(uint8_t evt_code)
        {
            return 0;
        }

        virtual int ZTC_trigger(std::shared_ptr<Event> evt, int ch)
        {
            return 0;
        }

        bool uploadEBill(my::string & ebill);

        ComService::ServerInfo get_server_info();
        const my::string& get_tag() const;

        JttClient& set_com_service(ComService* com_service);

        virtual int32_t getChnByCustomCh(uint8_t customCh)
        {
            return -1;
        }

    protected:
        virtual void resetAuthed() {;}
#define PROT_REC_TIMEOUT_DEFAULT            180
        void setProtRcvTimeout(int32_t sec)
        {
            mProtRcvTimeoutSec = sec;
        }
    protected:
        int mode; // 0：主，1：备
        bool mbNeedAuth = true;
        my::uint timeout[8]; // 超时常数: TCP, 注册, 鉴权, gps, 预警, 临时跟踪,心跳， gnss上传
        int tcp_retry_max; // 最大连接次数
        int reg_retry = 3;

        int mProtState; // 协议状态 bit0：在线 bit1注册 bit3：授权

        my::uint expiries[8]; // 超时时间: TCP, 注册, 鉴权, gps, 预警, 临时跟踪，心跳, gnss上传
        my::timestamp mOnlineTs;
        my::timestamp mCan1UploadTS;
        my::timestamp mCan2UploadTS;
        my::uint trace_expire;  // 位置跟踪结束时间, 0表示不开启
        my::uint artificialAlarmTime = 0; /* 人工确认告警触发时间 */
        my::spinlock lock; // 为保障sn一致性的锁

        my::timestamp mAccOffTS;
        my::uint mAccOffRptCount;

        my::string tag;

        my::string sim;
        ComService* com_service;
        bool is_main;

        std::map<my::ushort, shared_ptr<JttFtpUpload>> mFtpTasks;

        my::uchar iccard_state = 0; // IC卡状态, [1]: IC卡插入, [2]: IC卡拔出
        my::uint mRptState;     // 状态位表
        my::uint mRptAlarm;     // 告警位表
        int mAlarmLvlSpdThrs;/*50kmph as default*/
        struct {
            bool b0200NoAlarmAtt = false;
            bool bTTSPwrShort = false;
            uint32_t alarmMask = 0;
            uint32_t videoAlarmMask = 0;
        } mCustom;

        //auto snap
        my::timestamp mFixTmLastSnap[MAX_CHANNELS];
        std::map<my::uint64, std::string> mFixTmSnapFileList[MAX_CHANNELS];
        X8801       mSnapCmd;
        std::unordered_map<std::string, X8801> mSnap2send;
        bool mbDummy = false;
        std::map<my::ushort, my::ushort> mRcvCmdFilter;

        double mLastRptMileage = 0.0;
        my::timestamp mLastDisconnectTsMs = 0;
        my::timestamp mLastMsgRcvTs = 0;

        int32_t mProtRcvTimeoutSec = PROT_REC_TIMEOUT_DEFAULT;
        std::map<my::uint, int> mAreaStatus;
        my::ushort mSleepMode = 0;
        bool mLastSleepMode = false;

        /* 特殊报警录像达到存储阈值响应 0:还未收到响应1:已收到响应*/
        int mSpecialRecordAlarmRsp = -1;
};
#define AT_TASK_TIMEOUT_SEC (5 * 60)

// JTT部标附件任务
class JttAtCp2Disk : public my::thread
{
    public:
        JttAtCp2Disk() {}
        virtual ~JttAtCp2Disk()
        {
            stop();
        }

        int start()
        {
            return my::thread::start();
        }

        void stop()
        {
            my::thread::stop();
        }
        void add2cp(std::string path, bool bCp2sdcard = true)
        {
            if (bCp2sdcard) {
                MY_SPINLOCK_X(mCpLock);
                mAtPath2CpList.insert(std::pair<std::string, my::uint64>(path, my::timestamp::milliseconds_from_19700101()));
                logd("%ld : %s", my::timestamp::milliseconds_from_19700101(), path.c_str());
            }

            {
                std::string tmp = path.c_str();
                char * p = strrchr((char*)tmp.c_str(), '/');

                if (p) {
                    p[0] = 0;
                }

                string folder = tmp.c_str();
                auto it = mAtFolderList.find(folder);

                if (it == mAtFolderList.end()) {
                    MY_SPINLOCK_X(mClearLock);
                    mAtFolderList.insert(std::pair<std::string, my::uint64>(folder, my::timestamp::milliseconds_from_19700101()));
                }
            }
        }
    protected:
        void run();

    private:
        my::spinlock mCpLock;
        std::map<std::string, my::uint64> mAtPath2CpList;
        my::spinlock mClearLock;
        std::map<std::string, my::uint64> mAtFolderList;
};


// JTT部标附件任务
class JttAtTskMgr : public my::thread
{
    public:
        JttAtTskMgr() {}
        virtual ~JttAtTskMgr()
        {
            stop();
        }

        int start()
        {
            return my::thread::start();
        }

        void stop()
        {
            my::thread::stop();
        }
        void put(AlarmInfoItem &atInfo)
        {
            MY_SPINLOCK_X(mLock);

            my::string alarmTag = atInfo.alarm_tag.str(atInfo.protVer, atInfo.prot_subtype);
            auto item = mAtTaskList.find(std::string(alarmTag.c_str(), alarmTag.length()));
            if (item != mAtTaskList.end()) {
                if (item->second.alarm_id.length() > 0 && item->second.server_ip.length() > 0) {
                    /* 已更新 */
                    loge("alarm id exist already! %s\n", alarmTag.c_str());
                    return;
                }
            }

            for (auto it = atInfo.attachment.begin(); it != atInfo.attachment.end(); it++) {
                logd("path : %s", it->path.c_str());
            }

            mAtTaskList[std::string(alarmTag.c_str(), alarmTag.length())] = atInfo;

            if (mAtTaskList.size() > 20) {
                logw("too many task in list! %d\n", mAtTaskList.size());
            } else {
                AlarmInfoItem & aii = mAtTaskList[std::string(alarmTag.c_str(), alarmTag.length())];
                logd("+ %s:%d, %s", aii.server_ip.c_str(), aii.tcp_port, aii.alarm_id.c_str());
            }
        }
        std::string to_string(bool jt808_new, const  my::string & prot_subtype)
        {
            MY_SPINLOCK_X(mLock);
            std::string rs = "\n";
            int count = 0;

            if (mAtTaskList.size()) {
                std::map<std::string, AlarmInfoItem>::iterator it = mAtTaskList.begin();

                while (it != mAtTaskList.end()) {
                    rs += "----- [";
                    rs += std::to_string(count++);
                    rs += "] ";
                    rs += std::string(it->second.alarm_id.c_str(), it->second.alarm_id.length()) + " -----\n";
                    rs += it->second.to_string(jt808_new, prot_subtype);
                    it++;
                }
            }

            return rs;
        }
    protected:
        bool proc(AlarmInfoItem & atInfo, bool bWait);
        void run()
        {
            prctl(PR_SET_NAME, "alarmAttSnder");
            int slpMs = 0;
            while (!exiting()) {
                if (slpMs) {
                    my::thread::msleep(slpMs);
                    slpMs = 0;
                }
                //logd("at task mgr!\n");
                std::map<std::string, AlarmInfoItem>::iterator it;
                {
                    MY_SPINLOCK_X(mLock);

                    if (!mAtTaskList.size()) {
                        slpMs = 1000;
                        continue;

                    } else {
                        it = mAtTaskList.begin();
                        //logd("---> %s", it->second.alarm_id.c_str());
                    }
                }

                do {
                    auto t = time(NULL);
                    int diff = abs((int)(t - it->second.ts));
                    logd("timeaaa :%ld %u %d", t, it->second.ts, diff);
                    AlarmInfoItem atInfo = it->second;
                    bool succ = false;
                    if (atInfo.alarm_id.length() > 0 && atInfo.server_ip.length() > 0) {
                        succ = proc(it->second, (diff < 60));
                        logd("---> %s succ %d", atInfo.alarm_id.c_str(), succ);
                        if (succ) {
                            if (!access("/data/cp2sdcard", R_OK)) {
                                it->second.ts -= (AT_TASK_TIMEOUT_SEC - 60);/* wait 60s for cp*/
                            } else {
                                diff = AT_TASK_TIMEOUT_SEC + 1; /*force timeout to delete*/
                            }
                        }
                    } else {
                        logd("alarm_id or server_ip not ready!");
                        logd("alarmid: %s, server_ip: %s", atInfo.alarm_id.c_str(), atInfo.server_ip.c_str());
                    }

                    if (diff > AT_TASK_TIMEOUT_SEC) {
                        if (atInfo.attachment.size()) {
                            string cmd = "rm -r ";
                            string folder = atInfo.attachment[0].path.c_str();
                            
                            char* p = strstr((char *)folder.c_str(), "mprot");
                            char *p1 = strchr(p + 6, '/');
                            p1[0] = 0;

                            cmd += folder.c_str();
                            system(cmd.c_str());
                            logd("succ %d, diff %d, %s", succ, diff, cmd.c_str());
                        }

                        {
                            MY_SPINLOCK_X(mLock);
                            it = mAtTaskList.erase(it);
                        }

                    } else {
                        MY_SPINLOCK_X(mLock);
                        it++;
                    }

                    my::thread::msleep(50);
                } while (it != mAtTaskList.end());

                slpMs = 200;
            }
        }

    private:
        my::spinlock mLock; // tcp发送时候必须串行发送
        std::map<std::string, AlarmInfoItem> mAtTaskList;
};

// JTT部标TCP客户端
class JttTcpClient : public my::thread, public JttClient
{
    public:
        virtual int attSndLvl()
        {
            return 1 + (Manager::getInstance().config.sys.ignore_spdth == "false");
        }
        virtual bool ext_proc(const my::string& sim, my::ushort cmd, my::ushort sn, const my::constr& data)
        {
            return false;
        }
        virtual bool ext_procX8001(X8001 & rsp)
        {
            return false;
        }
        virtual bool ext_run_ahead()//before online check
        {
            return true;
        }
        virtual bool ext_run()
        {
            return true;
        }
        virtual bool ext_x0200_addition_info(const Current& st, X0200 & req)
        {
            return false;
        }
        virtual int procEvt(std::shared_ptr<Event> evt, int ch)
        {
            return 0;
        }

        virtual bool spdingChk() {
            return false;
        }
    public:
        JttTcpClient(const char * strSim, const char* tag)
            : JttClient(strSim), retry_connect(0)
        {
            mpAtTskMgr = NULL;
            mpAtCp = NULL;
            this->tag = tag;
            mLastTtsTm = my::timestamp::now();
            authorize_retry_connect = 0;
        }
        JttTcpClient(my::string ip, my::ushort port)
            : JttClient("fakeSim")
            , ip(ip), port(port), port2(0), retry_connect(0) {}

        std::string show_alarm_info()
        {
            if (mpAtTskMgr) {
                return mpAtTskMgr->to_string(mProtVersion, prot_subtype);
            }

            return "";
        }
        virtual ~JttTcpClient()
        {
            stop();
        }
        void regAtSnder(JttAtTskMgr * p)
        {
            MY_SPINLOCK_X(mLockAtSnder);
            mpAtTskMgr = p;
        }
        void regAtCper(JttAtCp2Disk * cp)
        {
            MY_SPINLOCK_X(mLockAtCper);
            mpAtCp = cp;
        }
        // 开启TCP客户端
        int start(const char* ip = "127.0.0.1", my::ushort port = 6160, const char* ip2 = "", my::ushort port2 = 0);

        // 重新加载客户端
        int reload(const char* ip = "127.0.0.1", my::ushort port = 6160, const char* ip2 = "", my::ushort port2 = 0);

        // 停止TCP客户端
        void stop();


        // TCP连接, 阻塞式
        int connect(bool block = false, int ms = 500);
        int recv_data(char * buf, int len)
        {
            int r = c.recv(buf, len);

            if (r == 0 || (r < 0 && !(my::net::is_nb_retrying()))) {
                logw("[JttTcpClient:run] Connection with %s:%d is lost, recv=[%d], error=[%s].", (!mode ? ip : ip2).c_str(), !mode ? port : port2, r, strerror(errno));
                retry_connect++;
                mProtState = JttClient::INVALID;
                expiries[0] = 0; // 立刻重连
                expiries[1] = 0; // 立刻重连
                resetAuthed(); // 立刻重连
                clear();
                return -1;
            }

            if (r > 0) {
                //logi("recv %d, %s", r, strerror(errno));
                //my::hexdump(my::constr(buf, r), false);
            }

            return r > 0 ? r : 0;
        }

        // 发送消息
        bool post(const my::constr& msg);

        // 接收消息
        bool get(my::string& msg);
        void send_attachment(AlarmInfoItem &ai)
        {
            MY_SPINLOCK_X(mLockAtSnder);
            if (mpAtTskMgr) {
                if (ai.attachment.size()) {
                    mpAtTskMgr->put(ai);
                }
            }
        }
        void add2cp(std::string path)
        {
            MY_SPINLOCK_X(mLockAtCper);
            if (mpAtCp) {
                mpAtCp->add2cp(path);
            }
        }
        virtual bool tired_drv_rpt();
        virtual bool tired_drv_tips(TripInfoT & trip, int timeLimit, bool black = false);
        virtual bool check_tired_drv(Current st);

        int canDataFeed(int canIdx, CanData & canData);

        virtual int gpsDataFeed(const Location& loc)
        {
            return 0;
        }

        /* chuanbiao ext */
        virtual int extern_canDataDeal(int canIdx, CanData & canData)
        {
            return false;
        }

    public:
        virtual bool chkRptCond(Current::AlarmBits * curAlarm, Current::AlarmBits * lastAlarm, Current::AlarmBits * chkAlarm,
                        Current::StateBits * curState, Current::StateBits * lastState);
        virtual int gnssSnd();
        virtual int gnssRpt();
        virtual int rptLocation()
        {
            Current st = ServiceHelper::getInstance().getStatus();
            return report(st);
        }
    protected:
        bool chkOnline(Current & st);
        bool chkRegStat();
        bool chkAuthed();
        bool chkSMS();

        int locationRpt(Current & st);
        //检测预警、盲区补传、IC卡状态
        virtual int chkAlarmRpt();

        int canDataRpt();

        // 消息接收
        virtual void run();

        virtual void resetAuthed()
        {
            expiries[2] = 0;
            authorize_retry_connect = 0;
        }
    private:
        my::spinlock lock; // tcp发送时候必须串行发送
        my::net::tcp::client c;

        my::string ip;
        my::ushort port;
        my::string ip2; // 备服务器
        my::ushort port2; // 备服务器端口

        int retry_connect; // 重连次数
        int authorize_retry_connect;//鉴权次数

        my::spinlock mLockAtSnder;
        JttAtTskMgr * mpAtTskMgr = NULL;

        my::spinlock mLockAtCper;
        JttAtCp2Disk * mpAtCp = NULL;

        my::spinlock lockcan1;
        char can1RecvTime[5] = {0};
        std::vector<CanData> can1Data;

        my::spinlock lockcan2;
        char can2RecvTime[5] = {0};
        std::vector<CanData> can2Data;

        int locRepTimeAdjust = 0;
    protected:
        struct {
            struct {
                time_t last_alarm_tm = 0;
                time_t last_warn_tm = 0;
                int alarm_count = 0;
                int warn_count = 0;
            } stats[2];/*0 white*/
            int restSpch_count = 0;
        } mTiredDrv;
    private:
        my::timestamp mLastTtsTm;
        uint32_t accOffRptCount = 0;
        uint32_t mbChkAlarmBits = 0;
};

#endif

