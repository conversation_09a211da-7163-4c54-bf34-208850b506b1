/*
 * Copyright [2025] MINIEYE
 * Descripttion : 文件描述
 * Author       : x<PERSON><PERSON><PERSON>n
 * Date         : 2025-05-08
 */
#include <time.h>
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <fcntl.h>
#include <termios.h>
#include <errno.h>
#include <string.h>

#include "CmdListener.h"
#include "prot.jtt808-1078_xiamenshuoqi.h"
#include "expand.receiver.h"
#include "service.helper.h"
#include "service.database.h"

struct XiaMenShuoQiAlarm {
    uint32_t alarm_id;                        // 报警ID
    uint8_t flag_status;                      // 标志状态
    uint8_t alarm_event_type;                 // 报警/事件类型
    uint8_t alarm_level;                      // 报警级别/制动级别
    uint8_t target_speed;                     // 目标车速 0xFF=不可用
    uint8_t target_obstacle_type;             // 目标障碍物类型
    uint8_t ttc_collision_time;               // TTC碰撞时间 单位100ms
    uint16_t longitudinal_relative_distance;  // 纵向相对距离 单位0.1m
    uint16_t lateral_relative_distance;       // 横向相对距离 单位0.1m
    uint8_t ultrasonic_distance;              // 超声波距离 单位分米
    uint8_t longitudinal_relative_velocity;    // 纵向相对速度 单位m/s
    uint8_t forward_collision_warning_level;  // 前向碰撞预警等级
    uint8_t lane_departure_warning;           // 车道线偏离预警
    uint8_t left_lane_line_type;              // 左车道线类型
    uint8_t right_lane_line_type;             // 右车道线类型
    uint8_t device_status_fault_code;         // 设备状态故障码
    uint8_t aeb_brake_switch_status;          // AEB制动开关状态 0xFF=无效
    uint8_t aeb_brake_status;                 // AEB制动状态
    uint16_t steering_wheel_angle;            // 方向盘角度 0xFFFF=无效
    uint8_t steering_wheel_status;            // 方向盘状态
    uint8_t gear_status;                      // 档位状态
    uint8_t accelerator_pedal_opening;        // 油门踏板开度 0xFF=无效
    uint8_t brake_pedal_opening;              // 刹车踏板开度 0xFF=无效
    uint8_t vehicle_speed;                    // 车速 单位km/h
    uint16_t altitude;                        // 高程 单位m
    uint32_t latitude;                        // 纬度 度*10^6
    uint32_t longitude;                       // 经度 度*10^6
    uint64_t ts;                              // 日期时间 (BCD[6]) YY-MM-DD-hh-mm-ss
    uint16_t vehicle_status;                  // 车辆状态
    AlarmTag alarm_tag;

    my::string str(bool jt808_new, const my::string& prot_subtype) const {
        my::string t;
        time2bcd(ts, t);
        my::string body;
        body << my::hton << alarm_id << flag_status << alarm_event_type << alarm_level << target_speed
             << target_obstacle_type << ttc_collision_time << longitudinal_relative_distance
             << lateral_relative_distance << ultrasonic_distance << longitudinal_relative_velocity
             << forward_collision_warning_level << lane_departure_warning << left_lane_line_type << right_lane_line_type
             << device_status_fault_code << aeb_brake_switch_status << aeb_brake_status << steering_wheel_angle
             << steering_wheel_status << gear_status << accelerator_pedal_opening << brake_pedal_opening
             << vehicle_speed << altitude << latitude << longitude << t << vehicle_status
             << alarm_tag.str(jt808_new, prot_subtype);

        my::string out;
        out << my::hton << (my::uchar)0xF0 << (my::uchar)body.length() << body;

        return out;
    }
};

JttTcpClient_xiamenshuoqi::JttTcpClient_xiamenshuoqi(const char* pSim, const char* tag)
    : JttTcpClient(pSim, tag) {
    prot_subtype = "xiamenshuoqi";

    ExpandReceiver::getInstance().setDataCallback(this, [this](msgpack::object obj) {
        logd("report xiamenshuoqi alarm");
        try {
            if (!(this->mProtState & AUTHORIZED)) {
                return;
            }
            expand::XiaMenShuoQiMsg msg = obj.as<expand::XiaMenShuoQiMsg>();
            ServiceHelper& sh = ServiceHelper::getInstance();
            Current st = sh.getStatus();
            X0200 req;
            req.lbi = sh.getLocationBaseInfo(st);
            XiaMenShuoQiAlarm alarm;

            alarm.ts = msg.ts;
            alarm.alarm_id = msg.alarm_id;
            alarm.flag_status = msg.flag_status;
            alarm.alarm_event_type = msg.alarm_event_type;
            alarm.alarm_level = msg.alarm_level;
            alarm.target_speed = msg.target_speed;
            alarm.target_obstacle_type = msg.target_obstacle_type;
            alarm.ttc_collision_time = msg.ttc_collision_time;
            alarm.longitudinal_relative_distance = msg.longitudinal_relative_distance;
            alarm.lateral_relative_distance = msg.lateral_relative_distance;
            alarm.ultrasonic_distance = msg.ultrasonic_distance;
            alarm.longitudinal_relative_velocity = msg.longitudinal_relative_velocity;
            alarm.forward_collision_warning_level = msg.forward_collision_warning_level;
            alarm.lane_departure_warning = msg.lane_departure_warning;
            alarm.left_lane_line_type = msg.left_lane_line_type;
            alarm.right_lane_line_type = msg.right_lane_line_type;
            alarm.device_status_fault_code = msg.device_status_fault_code;
            alarm.aeb_brake_switch_status = msg.aeb_brake_switch_status;
            alarm.aeb_brake_status = msg.aeb_brake_status;
            alarm.steering_wheel_angle = msg.steering_wheel_angle;
            alarm.steering_wheel_status = msg.steering_wheel_status;
            alarm.gear_status = msg.gear_status;
            alarm.accelerator_pedal_opening = msg.accelerator_pedal_opening;
            alarm.brake_pedal_opening = msg.brake_pedal_opening;
            alarm.vehicle_speed = msg.vehicle_speed;
            alarm.altitude = msg.altitude;
            alarm.latitude = msg.latitude;
            alarm.longitude = msg.longitude;
            alarm.vehicle_status = msg.vehicle_status;
            alarm.alarm_tag.term_id = com_service->si.id;
            alarm.alarm_tag.ts = msg.ts;  // second
            alarm.alarm_tag.sn = AlarmHelper::getInstance().fetchSn(msg.ts);
            alarm.alarm_tag.cnt = 0;

            // 保存信息到数据库
            AlarmInfoItem alarm_info;
            alarm_info.protVer = (com_service->si.attSndProtVer < 0) ? mProtVersion : com_service->si.attSndProtVer;
            alarm_info.prot_subtype = prot_subtype;
            alarm_info.term_id = com_service->si.id;  // 终端号
            alarm_info.ts = msg.ts;                   // 时间
            alarm_info.alarm_tag = alarm.alarm_tag;   // 报警标识
            alarm_info.state = 0;
            alarm_info.channel = 0xF0;
            alarm_info.event_type = alarm.alarm_event_type;

            logd("msg ts %u, tag ts %u, alarm_info ts %u", msg.ts, alarm.alarm_tag.ts, alarm_info.ts);

            my::string path;
            path.assignf("/mnt/obb/mprot/%s_chf0_xiamenshuoqi_%d_%p/",
                         my::timestamp::YYYYMMDD_HHMMSS(my::timestamp::milliseconds_from_19700101()).c_str(),
                         msg.alarm_id,
                         this);
            // 创建目录，如果不存在的话
            if (mkdir(path.c_str(), 0755) != 0 && errno != EEXIST) {
                logd("failed to create directory: %s, error: %s", path.c_str(), strerror(errno));
            }

            creat_shuoqi_accessory(&alarm, &alarm_info, &path);

            DbHelper& dh = DbHelper::getInstance();
            logd("put attachemnt: %s", alarm_info.to_string(false, "a").c_str());

            /* 保存数据库 */
            int ret = dh.addAlarmInfo(alarm_info);
            if (ret != 0) {
                my::file::rm(path.c_str());

                loge("addAlarmInfo");
            }
            send_attachment(alarm_info);
            logd("put attachemnt: %s", alarm_info.to_string(false, "a").c_str());

            req.lai_list[alarm_info.channel] = (alarm.str(mProtVersion, prot_subtype));

            logd("report xiamenshuoqi alarm %d, size %d, %xh",
                 alarm.alarm_id,
                 req.lai_list[alarm_info.channel].length(),
                 mProtState);
            report(st, req);
            return;

        } catch (...) {
            logd("fail to parse xiamenshuoqi alarm");
        }
    });
}

JttTcpClient_xiamenshuoqi::~JttTcpClient_xiamenshuoqi() {
    ExpandReceiver::getInstance().setDataCallback(this, nullptr);
}

int JttTcpClient_xiamenshuoqi::creat_shuoqi_accessory(XiaMenShuoQiAlarm* alarm,
                                                      AlarmInfoItem* alarm_info,
                                                      my::string* path) {
    int picNum = 3;
    time_t bgn = 0, end = 0;
    time_t cur = time(NULL);

    if (access("/data/no_video_att", R_OK)) {
        check_bgn_end_time(0, cur, bgn, end);
    }

    AlarmInfoAtItem att;
    att.path = *path;
    att.size = 0;
    att.at_state = 0;
    alarm_info->attachment.push_back(att);

    bool mp4PackAudio = prot_subtype == "henanTelecom" ? false : true;
    std::vector<int> accessoryCh;
    get_attachCh("adas", accessoryCh);
    int chCnt = accessoryCh.size();
    for (auto ch : accessoryCh) {
        my::string attCmd;
        int pics = 3;
        attCmd.assignf("cmd snapshot %d %d %d %s %d %d %d %d",
                       0,
                       alarm->alarm_tag.sn,
                       ch,
                       path->c_str(),
                       bgn,
                       end,
                       pics,
                       mp4PackAudio);

        if (!LogCallProxyCmd::sendReq("media", attCmd.c_str())) {  // ask media to product media files
            loge("cmd failed %s", attCmd.c_str());
        } else {
            loge("cmd  %s!\n", attCmd.c_str());
        }

        /* 填充adas附件信息 */
        if (access("/data/no_video_att", R_OK)) {
            // video
            AlarmInfoAtItem at;
            at.path.assignf("%s/%d.mp4", path->c_str(), ch);
            at.size = 0;
            at.at_state = 0;
            alarm_info->attachment.push_back(at);
            alarm->alarm_tag.cnt++;
        }
        // pic
        for (int i = 0; i < pics; i++) {
            AlarmInfoAtItem atJpg;
            atJpg.path.assignf("%s/%d.jpg", path->c_str(), i);
            atJpg.size = 0;
            atJpg.at_state = 0;
            alarm_info->attachment.push_back(atJpg);
            alarm->alarm_tag.cnt++;
        }
    }

    if (alarm->alarm_event_type == 0x02 /*AEB_BRAKING*/) {
        my::string attCmd;
        attCmd.assignf("cmd genAtt %s/0.bin", path->c_str());

        if (!LogCallProxyCmd::sendReq("xiaMenShuoQi_CanProt", attCmd.c_str())) {  // ask media to product media files
            loge("cmd failed %s", attCmd.c_str());
        } else {
            loge("cmd  %s!\n", attCmd.c_str());
        }

        AlarmInfoAtItem atJpg;
        atJpg.path.assignf("%s/0.bin", path->c_str());
        atJpg.size = 0;
        atJpg.at_state = 0;
        alarm_info->attachment.push_back(atJpg);  // 增加附件
        alarm->alarm_tag.cnt++;
    }
    return 0;
}
