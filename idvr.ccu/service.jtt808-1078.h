#ifndef __IDVRCORE_SERVICE_JTT808_1078_H__
#define __IDVRCORE_SERVICE_JTT808_1078_H__
#include <sys/prctl.h>
#include <vector>

#include "mystd.h"
#include "service.h"
#include "current.h"
#include "manager.h"
#include "service.helper.h"
#include "roadnetManager.h"
#include "prot.jtt808-1078_ttx.h"
#include "prot.jtt808-1078_sanyi.h"
#include "prot.jtt808-1078_njfht.h"
#include "service.jtt808-1078.msg.h"
#include "prot.jtt808-ZTC_MiniEye.h"
#include "prot.jtt808-1078_huoyun.h"
#include "prot.jtt808-1078_sichuan.h"
#include "prot.jtt808-1078_lvcheng.h"
#include "prot.jtt808-1078_nanjing.h"
#include "prot.jtt808-1078_guangan.h"
#include "prot.jtt808-1078_xizhong.h"
#include "prot.jtt808-1078_chengwei.h"
#include "prot.jtt808-1078_zhoushan.h"
#include "prot.jtt808-1078_shizheng.h"
#include "prot.jtt808-1078_njasiatek.h"
#include "prot.jtt808-1078_hualanTec.h"
#include "prot.jtt808-1078_chongqing.h"
#include "prot.jtt808-1078_guangdong.h"
#include "prot.jtt808-1078_xiangbiao.h"
#include "prot.jtt808-1078_lantaiyuan.h"
#include "prot.jtt808-1078_wuxiTianYi.h"
#include "prot.jtt808-1078_ningbozhatu.h"
#include "prot.jtt808-1078_jiangsuPuHuo.h"
#include "prot.jtt808-1078_nanjing_data.h"
#include "prot.jtt808-1078_henan_telecom.h"
#include "prot.jtt808-1078_xiamenshuoqi.h"

// JTT808-1078部标
class ServiceJtt808_1078 : public ComService
{
        friend class JttTcpClient;
    public:
        ServiceJtt808_1078(const char* tag, const char * sim, const char * protSubType);
        ~ServiceJtt808_1078();
        int postMsg(std :: shared_ptr < minieye :: AMessage > & spMsg) 
        {
            if (client) {
                spMsg->setTarget(client);
            } else if (second_client) {
                spMsg->setTarget(second_client);
            }

            return spMsg->post();
        }

        virtual bool gpsDataProc(const Location& loc) 
        {
            return (0 == client->gpsDataFeed(loc));
        }

        virtual bool canDataProc(int canIdx, CanData & data) 
        {
            return (0 == client->canDataFeed(canIdx, data));
        }
        virtual void displayWarnBmp(int level, int duration = 10)
        {
            client->displayWarnBmp(level, duration);
        }
        // 启动服务(可能是客户端, 也可能是服务端)
        int start(const ServerInfo& si);

        // 重新加载地址
        int reload(const ServerInfo& si);

        // 停止服务
        void stop();
        int protVersion()
        {
            return (mProtVersion > 2013);
        }
        void dummy(bool enable)
        {
            client->dummy(enable);
        }
        int deregister()
        {
            return client->deregister();
        }

        int queryServerTime()
        {
            return client->queryServerTime();
        }

        int responseDriverInfo()
        {
            return client->responseDriverInfo();
        }
        int rptGnss()
        {
            return client->gnssSnd();
        }

        int rptLocation()
        {
            return client->rptLocation();
        }

        int uploadEbill(my :: string & ebill)
        {
            return client->uploadEBill(ebill);
        }

        my::string get_type();
        int triggerAdasAlarm(const char * evt_name, int ch)
        {
            Current st = ServiceHelper::getInstance().getStatus();
            AdasAlarm alarm;
            EVT_TYPE e = name2value("adas", evt_name);

            if (e == EVT_TYPE_INVALID) {
                loge("bad evt name %s!", evt_name);
                return 0;
            }

            alarm.event_type = client->adasEvt2protEvt(e);
            alarm.evt_name = evt_name;
            //alarm.ts = (my::uint)time(NULL);
            alarm.ts_ms = my::timestamp::milliseconds_from_19700101();
            alarm.ts = (my::uint)(alarm.ts_ms / 1000);
            alarm.speed = (my::uchar)st.getSpeed();
            value2voice("adas", e);
            return client->adas_report(ch, alarm);
        }


        int triggerBsdAlarm(const char * evt_name, int ch)
        {
            int level = 0;
            const char * pBsd = strstr(evt_name, "BsdWarningL");
            if ((pBsd) && (1 == sscanf(pBsd, "BsdWarningL%d", &level))) {
                logd("level = %d", level);
            }

            ALERTOR_TRIGGER(level);
            client->displayWarnBmp(level);
            CAM_FULL_SCR(ch, 10);
            //value2voice("bsd", e);

            Current st = ServiceHelper::getInstance().getStatus();
            EVT_TYPE e = name2value("bsd", evt_name);

            if (e == EVT_TYPE_INVALID) {
                loge("bad evt name %s!", evt_name);
                return 0;
            }

            BsdAlarm alarm;
            alarm.event_type = client->bsdEvt2protEvt(e, evt_name);
            alarm.evt_name = evt_name;
            //alarm.ts =  (my::uint)time(NULL);
            alarm.ts_ms = my::timestamp::milliseconds_from_19700101();
            alarm.ts = (my::uint)(alarm.ts_ms / 1000);
            alarm.speed = (my::uchar)st.getSpeed();
            alarm.evt_level = level;
            value2voice("bsd", e);
            return client->bsd_report(ch, alarm);
        }

        int triggerDmsAlarm(const char * evt_name, int ch)
        {
            Current st = ServiceHelper::getInstance().getStatus();
            DmsAlarm alarm;
            EVT_TYPE e = name2value("dms", evt_name);

            if (e == EVT_TYPE_INVALID) {
                loge("bad evt name %s!", evt_name);
                return 0;
            }

            alarm.event_type = client->dmsEvt2protEvt(e);
            //alarm.ts = (my::uint)time(NULL);
            alarm.ts_ms = my::timestamp::milliseconds_from_19700101();
            alarm.ts = (my::uint)(alarm.ts_ms / 1000);
            alarm.speed = (my::uchar)st.getSpeed();
            alarm.evt_name = evt_name;
            value2voice("dms", e);
            return client->dms_report(ch, alarm);
        }

        int triggerSpeedingAlarm(const my::uchar alarmType, const my::uchar alarmStatus, const my::uchar roadSpeedLimit, int ch)
        {
            Current st = ServiceHelper::getInstance().getStatus();
            conf_t & cfg = Manager::getInstance().config.sys;
            speedingAlarm alarm;

            alarm.state = alarmStatus;
            alarm.event_type = 0x01;
            alarm.speedingType.value = alarmType;
            alarm.speedLimits = cfg.warn.overspeed.limit;
            
            alarm.ts_ms = my::timestamp::milliseconds_from_19700101();
            alarm.ts = (my::uint)(alarm.ts_ms / 1000);
            alarm.speed = (my::uchar)st.getSpeed(); /* 当前车速 */
            alarm.roadSpeedDiffs = roadSpeedLimit;  /* 通天星该项为道路限速阈值而非车速与道路限速值的差值 */
            if (2 == alarmStatus) {
                /* 避免由于位于临界速度附件在上传结束报警时速度又高于门限速度 */
                if (alarm.speedingType.roadSpeedingLimits) {
                    if (alarm.speed > alarm.roadSpeedDiffs) {
                        alarm.speed = alarm.roadSpeedDiffs - 1;
                    }
                } else if (alarm.speedingType.speedingLimits) {
                    if (alarm.speed > alarm.speedLimits) {
                        alarm.speed = alarm.speedLimits - 1;
                    } 
                }
            }
      
            logd("speed:%d, roadSpeedLimit:%d, roadSpeedDiffs:%d!\n", alarm.speed, roadSpeedLimit, alarm.roadSpeedDiffs);
            alarm.evt_name = "speeding";            
            return client->speeding_report(1, alarm);
        }

        int triggerOverHeightAlarm(const my::uchar alarmStatus, int ch, my::ushort limits)
        {
            overHeightAlarm alarm;

            alarm.state = alarmStatus;
            alarm.event_type = 0x01;
            alarm.carHeight = Manager::getInstance().config.sys.carHeight;
            #if 0
            RoadnetManager &roadHandler = RoadnetManager::getInstance();
            alarm.roadLimits = roadHandler.mRoadInfo.restrictInfo.heightInfo.value * 10;
            #else
            alarm.roadLimits = limits * 10;
            #endif
            
            alarm.ts_ms = my::timestamp::milliseconds_from_19700101();
            alarm.ts = (my::uint)(alarm.ts_ms / 1000);
            alarm.speed = (my::uchar)ServiceHelper::getInstance().getStatus().getSpeed(); /* 当前车速 */

      
            logd("carHeight:%d, roadLimits:%d!\n", alarm.carHeight, alarm.roadLimits);
            alarm.evt_name = "overHeight";            
            return client->over_height_report(1, alarm);
        }

        int triggerOverLoadAlarm(const my::uchar alarmType, const my::uchar alarmStatus, int ch, my::ushort loadVal, my::ushort limits)
        {
            overLoadAlarm alarm;
            Manager &m = Manager::getInstance();

            alarm.state = alarmStatus;
            alarm.event_type = alarmType;
            alarm.carLoad = loadVal * 10;
            alarm.roadLimits = limits * 10;
            alarm.ts_ms = my::timestamp::milliseconds_from_19700101();
            alarm.ts = (my::uint)(alarm.ts_ms / 1000);
            alarm.speed = (my::uchar)ServiceHelper::getInstance().getStatus().getSpeed(); /* 当前车速 */

      
            logd("carLoad:%d, roadLimits:%d!\n", alarm.carLoad, alarm.roadLimits);
            alarm.evt_name = "overLoad";            
            return client->over_load_report(1, alarm);
        }
        
        int triggerDumpperAlarm(const char * evt_name, int ch)
        {
            std::shared_ptr<Event> e = make_shared<Event>("dumper", EVT_TYPE_DUMPER_DETAIL);  //new Event(EVT_TYPE_DUMPER_DETAIL);
            if (!strcmp(evt_name, "full")) {
                e->c.event = "dumper_camera_full";
                e->c.u.dumper.full = true;
            } else if (!strcmp(evt_name, "empty")) {
                e->c.event = "dumper_camera_full";
                e->c.u.dumper.full = false;
            } else if (!strcmp(evt_name, "lift")) {
                e->c.event = "dumper_imu";
                e->c.u.dumper.lift = true;
            } else if (!strcmp(evt_name, "down")) {
                e->c.event = "dumper_imu";
                e->c.u.dumper.lift = false;
            } else if (!strcmp(evt_name, "cover")) {
                e->c.event = "dumper_camera_cover";
                e->c.u.dumper.cover = true;
            } else if (!strcmp(evt_name, "open")) {
                e->c.event = "dumper_camera_cover";
                e->c.u.dumper.cover = false;
            }
            
            return client->ZTC_trigger(e, ch);
        }


        int triggerZTCAlarm(const char * evt_name, int ch)
        {
            uint8_t evtCode = EVT_TYPE_ZTC_INVALID;
            if (!strcmp(evt_name, "overLoad")) {//超载 
                evtCode = EVT_TYPE_ZTC_OVERLOAD;
            } else if (!strcmp(evt_name, "liftInIllegalArea")) {//违规区域卸载 
                evtCode = EVT_TYPE_ZTC_LIFT_IN_BAN;
            } else if (!strcmp(evt_name, "driveOpenLid")) {//重车行驶厢盖未关闭 
                evtCode = EVT_TYPE_ZTC_DRIVE_OPEN_LID;
            } else if (!strcmp(evt_name, "driveNotAuth")) {//驾驶员身份验证或未进行身份验证启动车辆 
                evtCode = EVT_TYPE_ZTC_DRIVER_NOT_AUTH;
            } else if (!strcmp(evt_name, "tiredDrive")) {//疲劳驾驶或违规驾驶
                evtCode = EVT_TYPE_ZTC_TIRED_DRIVE;
            } else if (!strcmp(evt_name, "breakInBanArea")) {//重车闯禁
                evtCode = EVT_TYPE_ZTC_BREAK_IN_BAN;
            } else if (!strcmp(evt_name, "overSpeed")) {//重车超速
                evtCode = EVT_TYPE_ZTC_OVERSPEED;
            }
            
            return client->ZTC_report(evtCode);
        }
        int triggerVmsAlarm(const char * evt_name, int ch)
        {
            Current st = ServiceHelper::getInstance().getStatus();
            VmsAlarm alarm;
            memset(&alarm, 0, sizeof(VmsAlarm));
            EVT_TYPE e = name2value("vms", evt_name);
            if (e == EVT_TYPE_INVALID) {
                loge("bad evt name %s!", evt_name);
                return 0;
            }
            alarm.event_type = client->vmsEvt2protEvt(e);
            if (alarm.event_type == VMS_WARN_TYPE_INVALID) {
                loge("bad evt type %d!", alarm.event_type);
                return 0;
            }
            alarm.ts = (my::uint)time(NULL);
            alarm.speed = (my::uchar)st.getSpeed();
            alarm.evt_name = evt_name;
            value2voice("vms", e);
            return client->vms_report(ch, alarm);
        }

        int reportBsdAlarm(std::shared_ptr<Event> evt, int ch)
        {
            ALERTOR_TRIGGER(evt->c.level);
            if (mbBsdBmp) {
                client->displayWarnBmp(evt->c.level);
            }
            if (mbBsdFullsc) {
                CAM_FULL_SCR(ch, 10);
            }
            BsdAlarm alarm;
            int protEvt = client->bsdEvt2protEvt(evt->type(), evt->c.event.c_str());
            if (!protEvt) {
                loge("invalid prot evt %d", evt->type());
                return -1;
            }
            alarm.event_type = protEvt;
            alarm.ts = evt->c.ts / 1000;
            alarm.ts_ms = evt->c.ts;
            alarm.speed = (my::uchar)evt->c.speed;
            alarm.evt_name = evt->c.event.c_str();
            alarm.evt_level = evt->c.level;
            return client->bsd_report(ch, alarm);
        }
        int reportDmsAlarm(std::shared_ptr<Event> evt, int ch)
        {
            DmsAlarm alarm;
            int protEvt;
            EVT_TYPE type;
            if (EVT_TYPE_DMS_FACEID_MATCH_RESULT_V2 == evt->type()) {
                protEvt = client->dmsEvt2protEvt(EVT_TYPE_DMS_SNAP);
                type = EVT_TYPE_DMS_SNAP;
            } else {
                protEvt = client->dmsEvt2protEvt(evt->type());
                type = evt->type();
            }
            if (!protEvt) {
                loge("invalid prot evt %d", evt->type());
                return -1;
            }
            logd("protEvt %d\n", protEvt);
            alarm.event_type = protEvt;
            alarm.ts = evt->c.ts / 1000;
            alarm.ts_ms = evt->c.ts;
            alarm.speed = (my::uchar)evt->c.speed;
            const char * name = value2name("dms", type);
            logd("protEvt %s-ch:%d---\n", name, ch);
            alarm.evt_name = name;
            
            logd("protEvt %s\n", alarm.evt_name.c_str());
            return client->dms_report(ch, alarm);
        }

        int reportHodAlarm(std::shared_ptr<Event> evt, int ch)
        {
            /* HOD 报警消息结构与DMS一致 */
            DmsAlarm alarm;
            int protEvt;
            EVT_TYPE type;

            protEvt = client->hodEvt2protEvt(evt->type());
            type = evt->type();                
            if (!protEvt) {
                loge("invalid prot evt %d", evt->type());
                return -1;
            }
            logd("protEvt %d\n", protEvt);
            alarm.event_type = protEvt;
            alarm.ts = evt->c.ts / 1000;
            alarm.ts_ms = evt->c.ts;
            alarm.speed = (my::uchar)evt->c.speed;
            const char * name = value2name("hod", type);
            logd("protEvt %s-ch:%d---\n", name, ch);
            alarm.evt_name = name;
            
            logd("protEvt %s\n", alarm.evt_name.c_str());
            return client->hod_report(ch, alarm);    
        }
        int reportAdasAlarm(std::shared_ptr<Event> evt, int ch)
        {
            AdasAlarm alarm;
            int protEvt = client->adasEvt2protEvt(evt->type());
            if (!protEvt) {
                loge("invalid prot evt %d", evt->type());
                return -1;
            }
            alarm.event_type = protEvt;
            alarm.ts = evt->c.ts / 1000;
            alarm.ts_ms = evt->c.ts;
            alarm.speed = (my::uchar)evt->c.speed;
            alarm.evt_name = value2name("adas", evt->type());
            return client->adas_report(ch, alarm);
        }
        int reportOverHeightAlarm(std::shared_ptr<Event> evt, int ch)
        {
            overHeightAlarm alarm;
            const char *eventName = evt->c.event.c_str();
            if (NULL == eventName) {
                alarm.state = 1;
            } else {
                if (!strcmp("overHeightStart", eventName)) {
                    alarm.state = 1;
                } else if (!strcmp("overHeightContinue", eventName)) {
                    alarm.state = 3;
                } else {
                    alarm.state = 2;
                }
            }
            
            alarm.event_type = 0x01;
            alarm.ts = evt->c.ts / 1000;
            alarm.ts_ms = evt->c.ts;
            alarm.speed = (my::uchar)evt->c.speed;
            alarm.evt_name = eventName;
            return client->over_height_report(ch, alarm);
        }

        int reportVmsAlarm(std::shared_ptr<Event> evt, int ch)
        {
            VmsAlarm alarm;
            memset(&alarm, 0, sizeof(VmsAlarm));
            int protEvt = client->vmsEvt2protEvt(evt->type());
            alarm.event_type = protEvt;
            alarm.ts = evt->c.ts / 1000;
            alarm.ts_ms = evt->c.ts;
            alarm.speed = (my::uchar)evt->c.speed;
            alarm.evt_name = value2name("vms", evt->type());
            return client->vms_report(ch, alarm);
        }

        bool spdingChk() 
        {
            return client->spdingChk();
        }
        int procEvt(std::shared_ptr<Event> evt, int ch)
        {
            client->procEvt(evt, ch);
            return 0;
        }
        std::string showInfo(int type)
        {
            switch (type) {
                case 0:
                    if (client) {
                        return client->show_alarm_info();
                    } else if (second_client) {
                        return second_client->show_alarm_info();
                    }
                    break;
                default:
                    break;
            }

            return "";
        }
        bool connected(int idx = 0)
        {
            switch (idx) {
                case 0:
                    return client->connected();

                case 1:
                    return second_client->connected();

                default:
                    return false;
            }

            return false;
        }

    private:
        std::shared_ptr<JttTcpClient> client;
        std::shared_ptr<JttTcpClient> second_client;
        JttAtTskMgr * mAtTskMgr;
        JttAtCp2Disk* mAtCp2disk;
        bool mbBsdFullsc = true;
        bool mbBsdBmp = true;
};

#endif
